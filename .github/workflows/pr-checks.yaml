name: PR Checks

# PR Checks run on all PRs to the main branch and must pass before merging.
on:
  pull_request:
    branches:
      - main

  push:
    branches:
      - main

jobs:
  prettier-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - name: Install root dependencies
        if: steps.root-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Check code formatting with Prettier
        run: npx prettier --check "**/*.{js,jsx,ts,tsx,json,css,md}" --ignore-path .gitignore --ignore-path .prettierignore

  core-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup packages
        uses: ./.github/actions/setup-packages
      - name: Setup core component
        uses: ./.github/actions/setup-component
        with:
          component: core
          include-root: true

      - name: Type check and lint
        run: |
          cd core
          npx tsc --noEmit
          npm run lint
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

      - name: Run tests
        run: |
          cd core
          npm test
          npm run vitest
        env:
          IGNORE_API_KEY_TESTS: ${{ github.event.pull_request.head.repo.fork == true || github.actor == 'dependabot[bot]' }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          AZURE_FOUNDRY_CODESTRAL_API_KEY: ${{ secrets.AZURE_FOUNDRY_CODESTRAL_API_KEY }}
          AZURE_FOUNDRY_MISTRAL_SMALL_API_KEY: ${{ secrets.AZURE_FOUNDRY_MISTRAL_SMALL_API_KEY }}
          AZURE_OPENAI_GPT41_API_KEY: ${{ secrets.AZURE_OPENAI_GPT41_API_KEY }}

  gui-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup packages
        uses: ./.github/actions/setup-packages
      - name: Setup core component
        uses: ./.github/actions/setup-component
        with:
          component: core
          include-root: true
      - name: Setup gui component
        uses: ./.github/actions/setup-component
        with:
          component: gui
          include-packages: false
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Type check and lint
        run: |
          cd gui
          npx tsc --noEmit
          npm run lint

      - name: Run tests
        run: |
          cd gui
          npm test

  binary-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup packages
        uses: ./.github/actions/setup-packages
      - name: Setup core component
        uses: ./.github/actions/setup-component
        with:
          component: core
          include-root: true
      - name: Setup binary component
        uses: ./.github/actions/setup-component
        with:
          component: binary
          include-packages: false

      - name: Type check
        run: |
          cd binary
          npx tsc --noEmit

  vscode-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup packages
        uses: ./.github/actions/setup-packages
      - name: Setup core component
        uses: ./.github/actions/setup-component
        with:
          component: core
          include-root: true
      - name: Setup vscode component
        uses: ./.github/actions/setup-component
        with:
          component: vscode
          include-packages: false
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Type check and lint
        run: |
          cd extensions/vscode
          npm run write-build-timestamp
          npx tsc --noEmit
          npm run lint

      - name: Run vitest tests
        run: |
          cd extensions/vscode
          npm run vitest

  get-packages-matrix:
    runs-on: ubuntu-latest
    outputs:
      packages: ${{ steps.get-packages.outputs.packages }}
    steps:
      - uses: actions/checkout@v4
      - name: Get package list
        id: get-packages
        run: |
          packages=$(find packages/ -maxdepth 1 -type d -not -path packages/ -exec basename {} \; | sort | jq -R -s -c 'split("\n")[:-1]')
          echo "packages=$packages" >> $GITHUB_OUTPUT
          echo "Found packages: $packages"

  packages-checks:
    needs: get-packages-matrix
    runs-on: ubuntu-latest
    strategy:
      matrix:
        package: ${{ fromJson(needs.get-packages-matrix.outputs.packages) }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup packages
        uses: ./.github/actions/setup-packages

      - name: Test and check ${{ matrix.package }}
        run: |
          cd packages/${{ matrix.package }}
          npm ci
          npm test
        env:
          IGNORE_API_KEY_TESTS: ${{ github.event.pull_request.head.repo.fork == true || github.actor == 'dependabot[bot]' }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          AZURE_FOUNDRY_CODESTRAL_API_KEY: ${{ secrets.AZURE_FOUNDRY_CODESTRAL_API_KEY }}
          AZURE_FOUNDRY_MISTRAL_SMALL_API_KEY: ${{ secrets.AZURE_FOUNDRY_MISTRAL_SMALL_API_KEY }}
          AZURE_OPENAI_GPT41_API_KEY: ${{ secrets.AZURE_OPENAI_GPT41_API_KEY }}
          VOYAGE_API_KEY: ${{ secrets.VOYAGE_API_KEY }}

  vscode-get-test-file-matrix:
    runs-on: ubuntu-latest
    outputs:
      test_file_matrix: ${{ steps.vscode-get-test-file-matrix.outputs.test_file_matrix }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - name: Cache npm
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-npm-cache-matrix-${{ hashFiles('core/package-lock.json', 'extensions/vscode/package-lock.json') }}

      - name: Cache packages node_modules
        uses: actions/cache@v4
        with:
          path: |
            packages/*/node_modules
          key: ${{ runner.os }}-packages-node-modules-${{ hashFiles('packages/*/package-lock.json') }}

      - name: Cache core node modules
        uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Cache vscode extension node modules
        uses: actions/cache@v4
        id: vscode-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Build packages and get test files
        id: vscode-get-test-file-matrix
        run: |
          node ./scripts/build-packages.js
          cd extensions/vscode
          npm ci
          npm run e2e:compile
          if [[ "${{ github.event.pull_request.head.repo.fork }}" == "true" || "${{ github.actor }}" == "dependabot[bot]" ]]; then
            # Exclude SSH tests for forks
            FILES=$(ls -1 e2e/_output/tests/*.test.js | grep -v "SSH" | jq -R . | jq -s .)
          else
            # Include all tests for non-forks
            FILES=$(ls -1 e2e/_output/tests/*.test.js | jq -R . | jq -s .)
          fi
          echo "test_file_matrix<<EOF" >> $GITHUB_OUTPUT
          echo "$FILES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
        env:
          # https://github.com/microsoft/vscode-ripgrep/issues/9#issuecomment-643965333
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Debug Outputs
        run: |
          echo "Test files: ${{ steps.vscode-get-test-file-matrix.outputs.test_file_matrix }}"

  vscode-package-extension-linux:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Build VS Code extension
        uses: ./.github/actions/build-vscode-extension
        with:
          platform: linux
          arch: x64
          npm_config_arch: x64
          pre-release: false
          commit-sha: ${{ github.sha }}
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: vscode-extension-build-Linux
          path: extensions/vscode/build

  vscode-e2e-tests:
    name: ${{ matrix.test_file || 'unknown' }} (${{ matrix.command }})
    needs: [vscode-get-test-file-matrix, vscode-package-extension-linux]
    runs-on: ubuntu-latest
    timeout-minutes: 15
    # Tests requiring secrets need approval from maintainers
    strategy:
      fail-fast: false
      matrix:
        test_file: ${{ fromJson(needs.vscode-get-test-file-matrix.outputs.test_file_matrix) }}
        command: ["e2e:ci:run", "e2e:ci:run-yaml"]
    steps:
      - uses: actions/checkout@v4
      - name: Run VS Code E2E test
        uses: ./.github/actions/run-vscode-e2e-test
        with:
          test_file: ${{ matrix.test_file }}
          command: ${{ matrix.command }}
          ssh_key: ${{ secrets.GH_ACTIONS_SSH_TEST_KEY_PEM }}
          ssh_host: ${{ secrets.GH_ACTIONS_SSH_TEST_DNS_NAME }}
          is_fork: ${{ github.event.pull_request.head.repo.fork == true || github.actor == 'dependabot[bot]' }}

  jetbrains-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4
      - name: Run JetBrains tests
        uses: ./.github/actions/run-jetbrains-tests
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          ci-github-token: ${{ secrets.CI_GITHUB_TOKEN }}

  # GitHub does not have a way of requiring that all checks pass (you must manually select each job)
  # This action at least lets us manage the list of required tests via source control
  # so that creators of new jobs can add them to this list
  require-all-checks-to-pass:
    if: always()
    runs-on: ubuntu-latest
    needs:
      - prettier-check
      - core-checks
      - gui-checks
      - binary-checks
      - vscode-checks
      - get-packages-matrix
      - packages-checks
      - vscode-get-test-file-matrix
      - vscode-package-extension-linux
      - vscode-e2e-tests
      - jetbrains-tests

    steps:
      - name: Decide whether the needed jobs succeeded or failed
        uses: re-actors/alls-green@release/v1
        with:
          jobs: ${{ toJSON(needs) }}
