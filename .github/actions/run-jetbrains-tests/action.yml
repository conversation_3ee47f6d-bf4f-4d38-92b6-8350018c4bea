name: "Run JetBrains Tests"
description: "Setup and run JetBrains IntelliJ tests with all dependencies"

inputs:
  github-token:
    description: "GitHub token for accessing repositories"
    required: true
  ci-github-token:
    description: "CI GitHub token for vscode extension build"
    required: true

runs:
  using: "composite"
  steps:
    - uses: actions/setup-node@v4
      with:
        node-version-file: ".nvmrc"

    - uses: actions/cache@v4
      with:
        path: core/node_modules
        key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

    - name: Build packages and install core dependencies
      shell: bash
      run: |
        node ./scripts/build-packages.js
        cd core
        npm ci

    - name: Setup Java
      uses: actions/setup-java@v4.5.0
      with:
        distribution: zulu
        java-version: 17

    - name: Setup FFmpeg
      uses: AnimMouse/setup-ffmpeg@v1
      with:
        token: ${{ inputs.github-token }}

    - name: Setup Gradle
      uses: gradle/actions/setup-gradle@v3

    - uses: actions/cache@v4
      with:
        path: ~/.npm
        key: ${{ runner.os }}-npm-cache-jetbrains-${{ hashFiles('extensions/vscode/package-lock.json') }}

    - uses: actions/cache@v4
      id: vscode-extension-cache
      with:
        path: extensions/vscode/node_modules
        key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

    - uses: actions/cache@v4
      id: gui-cache
      with:
        path: gui/node_modules
        key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

    # Cache prepackaged extension build to share with other jobs
    - uses: actions/cache@v4
      id: vscode-prepackage-cache
      with:
        path: extensions/vscode/build
        key: ${{ runner.os }}-vscode-prepackage-${{ hashFiles('extensions/vscode/package-lock.json', 'extensions/vscode/**/*.ts', 'extensions/vscode/**/*.json') }}

    # Only run prepackage if not cached - saves ~1 minute
    - name: Run prepackage script
      if: steps.vscode-prepackage-cache.outputs.cache-hit != 'true'
      shell: bash
      run: |
        cd extensions/vscode
        if [ "${{ steps.vscode-extension-cache.outputs.cache-hit }}" != "true" ]; then
          npm ci
        fi
        npm run prepackage
      env:
        # https://github.com/microsoft/vscode-ripgrep/issues/9#issuecomment-643965333
        GITHUB_TOKEN: ${{ inputs.ci-github-token }}

    - uses: actions/cache@v4
      id: binary-cache
      with:
        path: binary/node_modules
        key: ${{ runner.os }}-binary-node-modules-${{ hashFiles('binary/package-lock.json') }}

    - name: Build the binaries
      shell: bash
      run: |
        cd binary
        npm run build

    - name: Start test IDE
      shell: bash
      run: |
        cd extensions/intellij
        export DISPLAY=:99.0
        Xvfb -ac :99 -screen 0 1920x1080x24 &
        sleep 10
        mkdir -p build/reports
        ./gradlew runIdeForUiTests &

    - name: Wait for JB connection
      uses: jtalk/url-health-check-action@v3
      with:
        url: http://127.0.0.1:8082
        max-attempts: 15
        retry-delay: 30s

    - name: Run tests
      shell: bash
      run: |
        cd extensions/intellij
        export DISPLAY=:99.0
        ./gradlew test

    - name: Move video
      if: ${{ failure() }}
      shell: bash
      run: |
        cd extensions/intellij
        mv video build/reports

    - name: Copy logs
      if: ${{ failure() }}
      shell: bash
      run: |
        cd extensions/intellij
        mv build/idea-sandbox/system/log/ build/reports

    - name: Save fails report
      if: ${{ failure() }}
      uses: actions/upload-artifact@v4
      with:
        name: jb-failure-report
        path: |
          ${{ github.workspace }}/extensions/intellij/build/reports
