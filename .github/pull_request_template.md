## Description

[ What changed? Feel free to be brief. ]

## Checklist

- [] I've read the [contributing guide](https://github.com/continuedev/continue/blob/main/CONTRIBUTING.md)
- [] The relevant docs, if any, have been updated or created
- [] The relevant tests, if any, have been updated or created

## Screen recording or screenshot

[ When applicable, please include a short screen recording or screenshot - this makes it much easier for us as contributors to review and understand your changes. See [this PR](https://github.com/continuedev/continue/pull/6455) as a good example. ]

## Tests

[ What tests were added or updated to ensure the changes work as expected? ]
