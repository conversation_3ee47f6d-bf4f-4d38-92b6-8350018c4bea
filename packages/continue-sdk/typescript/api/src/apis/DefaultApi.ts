/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from "../runtime";
import type {
  GetAssistant200Response,
  GetAssistant403Response,
  GetAssistant404Response,
  GetFreeTrialStatus200Response,
  GetModelsAddOnCheckoutUrl200Response,
  GetModelsAddOnCheckoutUrl500Response,
  GetPolicy200Response,
  ListAssistantFullSlugs429Response,
  ListAssistants200ResponseInner,
  ListAssistants401Response,
  ListAssistants404Response,
  ListOrganizations200Response,
  SyncSecretsRequest,
} from "../models/index";
import {
  GetAssistant200ResponseFromJSON,
  GetAssistant200ResponseToJSON,
  GetAssistant403ResponseFromJSON,
  GetAssistant403ResponseToJSON,
  GetAssistant404ResponseFromJSON,
  GetAssistant404ResponseToJSON,
  GetFreeTrialStatus200ResponseFromJSON,
  GetFreeTrialStatus200ResponseToJSON,
  GetModelsAddOnCheckoutUrl200ResponseFromJSON,
  GetModelsAddOnCheckoutUrl200ResponseToJSON,
  GetModelsAddOnCheckoutUrl500ResponseFromJSON,
  GetModelsAddOnCheckoutUrl500ResponseToJSON,
  GetPolicy200ResponseFromJSON,
  GetPolicy200ResponseToJSON,
  ListAssistantFullSlugs429ResponseFromJSON,
  ListAssistantFullSlugs429ResponseToJSON,
  ListAssistants200ResponseInnerFromJSON,
  ListAssistants200ResponseInnerToJSON,
  ListAssistants401ResponseFromJSON,
  ListAssistants401ResponseToJSON,
  ListAssistants404ResponseFromJSON,
  ListAssistants404ResponseToJSON,
  ListOrganizations200ResponseFromJSON,
  ListOrganizations200ResponseToJSON,
  SyncSecretsRequestFromJSON,
  SyncSecretsRequestToJSON,
} from "../models/index";

export interface GetAssistantRequest {
  ownerSlug: string;
  packageSlug: string;
  alwaysUseProxy?: GetAssistantAlwaysUseProxyEnum;
  organizationId?: string;
}

export interface GetModelsAddOnCheckoutUrlRequest {
  profileId?: string;
  vscodeUriScheme?: string;
}

export interface ListAssistantsRequest {
  alwaysUseProxy?: ListAssistantsAlwaysUseProxyEnum;
  organizationId?: string;
}

export interface SyncSecretsOperationRequest {
  syncSecretsRequest: SyncSecretsRequest;
}

/**
 * DefaultApi - interface
 *
 * @export
 * @interface DefaultApiInterface
 */
export interface DefaultApiInterface {
  /**
   * Returns a single assistant configuration by its owner and package slug. This endpoint is useful when you need to retrieve or refresh a specific assistant without fetching the entire list.
   * @summary Get a specific assistant by slug
   * @param {string} ownerSlug Slug of the user or organization that owns the assistant
   * @param {string} packageSlug Slug of the assistant package
   * @param {'true' | 'false'} [alwaysUseProxy] Whether to always use the Continue-managed proxy for model requests
   * @param {string} [organizationId] ID of the organization to scope assistants to. If not provided, personal assistants are returned.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  getAssistantRaw(
    requestParameters: GetAssistantRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetAssistant200Response>>;

  /**
   * Returns a single assistant configuration by its owner and package slug. This endpoint is useful when you need to retrieve or refresh a specific assistant without fetching the entire list.
   * Get a specific assistant by slug
   */
  getAssistant(
    requestParameters: GetAssistantRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetAssistant200Response>;

  /**
   * Returns the current free trial status for the authenticated user, including usage counts and limits for chat and autocomplete features.
   * @summary Get free trial status for user
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  getFreeTrialStatusRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetFreeTrialStatus200Response>>;

  /**
   * Returns the current free trial status for the authenticated user, including usage counts and limits for chat and autocomplete features.
   * Get free trial status for user
   */
  getFreeTrialStatus(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetFreeTrialStatus200Response>;

  /**
   * Creates a Stripe checkout session for the models add-on subscription and returns the checkout URL.
   * @summary Get Stripe checkout URL for models add-on
   * @param {string} [profileId] Profile ID to include in the callback URL
   * @param {string} [vscodeUriScheme] VS Code URI scheme to include in the callback URL
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  getModelsAddOnCheckoutUrlRaw(
    requestParameters: GetModelsAddOnCheckoutUrlRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetModelsAddOnCheckoutUrl200Response>>;

  /**
   * Creates a Stripe checkout session for the models add-on subscription and returns the checkout URL.
   * Get Stripe checkout URL for models add-on
   */
  getModelsAddOnCheckoutUrl(
    requestParameters: GetModelsAddOnCheckoutUrlRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetModelsAddOnCheckoutUrl200Response>;

  /**
   * Returns the policy configuration for the first organization that the user belongs to which has a policy configured.
   * @summary Get organization policy
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  getPolicyRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetPolicy200Response>>;

  /**
   * Returns the policy configuration for the first organization that the user belongs to which has a policy configured.
   * Get organization policy
   */
  getPolicy(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetPolicy200Response>;

  /**
   * This endpoint is temporarily disabled and returns a 429 status code to prevent constant refreshes of the full assistant list until a fixed client version can be deployed.
   * @summary List assistant full slugs (currently returns 429)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  listAssistantFullSlugsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * This endpoint is temporarily disabled and returns a 429 status code to prevent constant refreshes of the full assistant list until a fixed client version can be deployed.
   * List assistant full slugs (currently returns 429)
   */
  listAssistantFullSlugs(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * @summary List assistants for IDE
   * @param {'true' | 'false'} [alwaysUseProxy] Whether to always use the Continue-managed proxy for model requests
   * @param {string} [organizationId] ID of the organization to scope assistants to. If not provided, personal assistants are returned.
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  listAssistantsRaw(
    requestParameters: ListAssistantsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<ListAssistants200ResponseInner>>>;

  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * List assistants for IDE
   */
  listAssistants(
    requestParameters: ListAssistantsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<ListAssistants200ResponseInner>>;

  /**
   * Returns a list of organizations that the authenticated user belongs to, including organization metadata and pre-signed icon URLs.
   * @summary List organizations for user
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  listOrganizationsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListOrganizations200Response>>;

  /**
   * Returns a list of organizations that the authenticated user belongs to, including organization metadata and pre-signed icon URLs.
   * List organizations for user
   */
  listOrganizations(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListOrganizations200Response>;

  /**
   * Resolves and synchronizes secrets for the authenticated user based on the provided Fully Qualified Secret Names (FQSNs).
   * @summary Synchronize secrets for user
   * @param {SyncSecretsRequest} syncSecretsRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof DefaultApiInterface
   */
  syncSecretsRaw(
    requestParameters: SyncSecretsOperationRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<object | null>>>;

  /**
   * Resolves and synchronizes secrets for the authenticated user based on the provided Fully Qualified Secret Names (FQSNs).
   * Synchronize secrets for user
   */
  syncSecrets(
    requestParameters: SyncSecretsOperationRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<object | null>>;
}

/**
 *
 */
export class DefaultApi extends runtime.BaseAPI implements DefaultApiInterface {
  /**
   * Returns a single assistant configuration by its owner and package slug. This endpoint is useful when you need to retrieve or refresh a specific assistant without fetching the entire list.
   * Get a specific assistant by slug
   */
  async getAssistantRaw(
    requestParameters: GetAssistantRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetAssistant200Response>> {
    if (requestParameters["ownerSlug"] == null) {
      throw new runtime.RequiredError(
        "ownerSlug",
        'Required parameter "ownerSlug" was null or undefined when calling getAssistant().',
      );
    }

    if (requestParameters["packageSlug"] == null) {
      throw new runtime.RequiredError(
        "packageSlug",
        'Required parameter "packageSlug" was null or undefined when calling getAssistant().',
      );
    }

    const queryParameters: any = {};

    if (requestParameters["alwaysUseProxy"] != null) {
      queryParameters["alwaysUseProxy"] = requestParameters["alwaysUseProxy"];
    }

    if (requestParameters["organizationId"] != null) {
      queryParameters["organizationId"] = requestParameters["organizationId"];
    }

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/get-assistant/{ownerSlug}/{packageSlug}`
          .replace(
            `{${"ownerSlug"}}`,
            encodeURIComponent(String(requestParameters["ownerSlug"])),
          )
          .replace(
            `{${"packageSlug"}}`,
            encodeURIComponent(String(requestParameters["packageSlug"])),
          ),
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      GetAssistant200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Returns a single assistant configuration by its owner and package slug. This endpoint is useful when you need to retrieve or refresh a specific assistant without fetching the entire list.
   * Get a specific assistant by slug
   */
  async getAssistant(
    requestParameters: GetAssistantRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetAssistant200Response> {
    const response = await this.getAssistantRaw(
      requestParameters,
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Returns the current free trial status for the authenticated user, including usage counts and limits for chat and autocomplete features.
   * Get free trial status for user
   */
  async getFreeTrialStatusRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetFreeTrialStatus200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/free-trial-status`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      GetFreeTrialStatus200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Returns the current free trial status for the authenticated user, including usage counts and limits for chat and autocomplete features.
   * Get free trial status for user
   */
  async getFreeTrialStatus(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetFreeTrialStatus200Response> {
    const response = await this.getFreeTrialStatusRaw(initOverrides);
    return await response.value();
  }

  /**
   * Creates a Stripe checkout session for the models add-on subscription and returns the checkout URL.
   * Get Stripe checkout URL for models add-on
   */
  async getModelsAddOnCheckoutUrlRaw(
    requestParameters: GetModelsAddOnCheckoutUrlRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetModelsAddOnCheckoutUrl200Response>> {
    const queryParameters: any = {};

    if (requestParameters["profileId"] != null) {
      queryParameters["profile_id"] = requestParameters["profileId"];
    }

    if (requestParameters["vscodeUriScheme"] != null) {
      queryParameters["vscode_uri_scheme"] =
        requestParameters["vscodeUriScheme"];
    }

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/get-models-add-on-checkout-url`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      GetModelsAddOnCheckoutUrl200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Creates a Stripe checkout session for the models add-on subscription and returns the checkout URL.
   * Get Stripe checkout URL for models add-on
   */
  async getModelsAddOnCheckoutUrl(
    requestParameters: GetModelsAddOnCheckoutUrlRequest = {},
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetModelsAddOnCheckoutUrl200Response> {
    const response = await this.getModelsAddOnCheckoutUrlRaw(
      requestParameters,
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Returns the policy configuration for the first organization that the user belongs to which has a policy configured.
   * Get organization policy
   */
  async getPolicyRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<GetPolicy200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/policy`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      GetPolicy200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Returns the policy configuration for the first organization that the user belongs to which has a policy configured.
   * Get organization policy
   */
  async getPolicy(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<GetPolicy200Response> {
    const response = await this.getPolicyRaw(initOverrides);
    return await response.value();
  }

  /**
   * This endpoint is temporarily disabled and returns a 429 status code to prevent constant refreshes of the full assistant list until a fixed client version can be deployed.
   * List assistant full slugs (currently returns 429)
   */
  async listAssistantFullSlugsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/list-assistant-full-slugs`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * This endpoint is temporarily disabled and returns a 429 status code to prevent constant refreshes of the full assistant list until a fixed client version can be deployed.
   * List assistant full slugs (currently returns 429)
   */
  async listAssistantFullSlugs(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.listAssistantFullSlugsRaw(initOverrides);
  }

  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * List assistants for IDE
   */
  async listAssistantsRaw(
    requestParameters: ListAssistantsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<ListAssistants200ResponseInner>>> {
    const queryParameters: any = {};

    if (requestParameters["alwaysUseProxy"] != null) {
      queryParameters["alwaysUseProxy"] = requestParameters["alwaysUseProxy"];
    }

    if (requestParameters["organizationId"] != null) {
      queryParameters["organizationId"] = requestParameters["organizationId"];
    }

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/list-assistants`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(ListAssistants200ResponseInnerFromJSON),
    );
  }

  /**
   * Returns a complete list of assistants available to the user, with their full configurations, icons, and other metadata needed by the IDE to display and use them.  This endpoint performs a full refresh of the list of assistants, including unrolling configurations and resolving secrets.
   * List assistants for IDE
   */
  async listAssistants(
    requestParameters: ListAssistantsRequest = {},
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<ListAssistants200ResponseInner>> {
    const response = await this.listAssistantsRaw(
      requestParameters,
      initOverrides,
    );
    return await response.value();
  }

  /**
   * Returns a list of organizations that the authenticated user belongs to, including organization metadata and pre-signed icon URLs.
   * List organizations for user
   */
  async listOrganizationsRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ListOrganizations200Response>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/list-organizations`,
        method: "GET",
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ListOrganizations200ResponseFromJSON(jsonValue),
    );
  }

  /**
   * Returns a list of organizations that the authenticated user belongs to, including organization metadata and pre-signed icon URLs.
   * List organizations for user
   */
  async listOrganizations(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ListOrganizations200Response> {
    const response = await this.listOrganizationsRaw(initOverrides);
    return await response.value();
  }

  /**
   * Resolves and synchronizes secrets for the authenticated user based on the provided Fully Qualified Secret Names (FQSNs).
   * Synchronize secrets for user
   */
  async syncSecretsRaw(
    requestParameters: SyncSecretsOperationRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<object | null>>> {
    if (requestParameters["syncSecretsRequest"] == null) {
      throw new runtime.RequiredError(
        "syncSecretsRequest",
        'Required parameter "syncSecretsRequest" was null or undefined when calling syncSecrets().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters["Content-Type"] = "application/json";

    if (this.configuration && this.configuration.accessToken) {
      const token = this.configuration.accessToken;
      const tokenString = await token("apiKeyAuth", []);

      if (tokenString) {
        headerParameters["Authorization"] = `Bearer ${tokenString}`;
      }
    }
    const response = await this.request(
      {
        path: `/ide/sync-secrets`,
        method: "POST",
        headers: headerParameters,
        query: queryParameters,
        body: SyncSecretsRequestToJSON(requestParameters["syncSecretsRequest"]),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse<any>(response);
  }

  /**
   * Resolves and synchronizes secrets for the authenticated user based on the provided Fully Qualified Secret Names (FQSNs).
   * Synchronize secrets for user
   */
  async syncSecrets(
    requestParameters: SyncSecretsOperationRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<object | null>> {
    const response = await this.syncSecretsRaw(
      requestParameters,
      initOverrides,
    );
    return await response.value();
  }
}

/**
 * @export
 */
export const GetAssistantAlwaysUseProxyEnum = {
  TRUE: "true",
  FALSE: "false",
} as const;
export type GetAssistantAlwaysUseProxyEnum =
  (typeof GetAssistantAlwaysUseProxyEnum)[keyof typeof GetAssistantAlwaysUseProxyEnum];
/**
 * @export
 */
export const ListAssistantsAlwaysUseProxyEnum = {
  TRUE: "true",
  FALSE: "false",
} as const;
export type ListAssistantsAlwaysUseProxyEnum =
  (typeof ListAssistantsAlwaysUseProxyEnum)[keyof typeof ListAssistantsAlwaysUseProxyEnum];
