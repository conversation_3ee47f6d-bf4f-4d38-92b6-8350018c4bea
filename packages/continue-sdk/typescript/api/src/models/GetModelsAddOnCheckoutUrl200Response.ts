/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface GetModelsAddOnCheckoutUrl200Response
 */
export interface GetModelsAddOnCheckoutUrl200Response {
  /**
   * Stripe checkout session URL
   * @type {string}
   * @memberof GetModelsAddOnCheckoutUrl200Response
   */
  url: string;
}

/**
 * Check if a given object implements the GetModelsAddOnCheckoutUrl200Response interface.
 */
export function instanceOfGetModelsAddOnCheckoutUrl200Response(
  value: object,
): value is GetModelsAddOnCheckoutUrl200Response {
  if (!("url" in value) || value["url"] === undefined) return false;
  return true;
}

export function GetModelsAddOnCheckoutUrl200ResponseFromJSON(
  json: any,
): GetModelsAddOnCheckoutUrl200Response {
  return GetModelsAddOnCheckoutUrl200ResponseFromJSONTyped(json, false);
}

export function GetModelsAddOnCheckoutUrl200ResponseFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): GetModelsAddOnCheckoutUrl200Response {
  if (json == null) {
    return json;
  }
  return {
    url: json["url"],
  };
}

export function GetModelsAddOnCheckoutUrl200ResponseToJSON(
  json: any,
): GetModelsAddOnCheckoutUrl200Response {
  return GetModelsAddOnCheckoutUrl200ResponseToJSONTyped(json, false);
}

export function GetModelsAddOnCheckoutUrl200ResponseToJSONTyped(
  value?: GetModelsAddOnCheckoutUrl200Response | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    url: value["url"],
  };
}
