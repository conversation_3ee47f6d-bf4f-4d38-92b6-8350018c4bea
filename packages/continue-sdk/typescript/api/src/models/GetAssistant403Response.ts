/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface GetAssistant403Response
 */
export interface GetAssistant403Response {
  /**
   *
   * @type {string}
   * @memberof GetAssistant403Response
   */
  message?: string;
}

/**
 * Check if a given object implements the GetAssistant403Response interface.
 */
export function instanceOfGetAssistant403Response(
  value: object,
): value is GetAssistant403Response {
  return true;
}

export function GetAssistant403ResponseFromJSON(
  json: any,
): GetAssistant403Response {
  return GetAssistant403ResponseFromJSONTyped(json, false);
}

export function GetAssistant403ResponseFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): GetAssistant403Response {
  if (json == null) {
    return json;
  }
  return {
    message: json["message"] == null ? undefined : json["message"],
  };
}

export function GetAssistant403ResponseToJSON(
  json: any,
): GetAssistant403Response {
  return GetAssistant403ResponseToJSONTyped(json, false);
}

export function GetAssistant403ResponseToJSONTyped(
  value?: GetAssistant403Response | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value["message"],
  };
}
