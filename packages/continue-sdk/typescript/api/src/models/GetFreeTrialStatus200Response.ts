/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface GetFreeTrialStatus200Response
 */
export interface GetFreeTrialStatus200Response {
  /**
   * Whether the user has opted into the free trial
   * @type {boolean}
   * @memberof GetFreeTrialStatus200Response
   */
  optedInToFreeTrial: boolean;
  /**
   * Current number of chat messages used
   * @type {number}
   * @memberof GetFreeTrialStatus200Response
   */
  chatCount?: number | null;
  /**
   * Current number of autocomplete requests used
   * @type {number}
   * @memberof GetFreeTrialStatus200Response
   */
  autocompleteCount?: number | null;
  /**
   * Maximum number of chat messages allowed in free trial
   * @type {number}
   * @memberof GetFreeTrialStatus200Response
   */
  chatLimit: number;
  /**
   * Maximum number of autocomplete requests allowed in free trial
   * @type {number}
   * @memberof GetFreeTrialStatus200Response
   */
  autocompleteLimit: number;
}

/**
 * Check if a given object implements the GetFreeTrialStatus200Response interface.
 */
export function instanceOfGetFreeTrialStatus200Response(
  value: object,
): value is GetFreeTrialStatus200Response {
  if (
    !("optedInToFreeTrial" in value) ||
    value["optedInToFreeTrial"] === undefined
  )
    return false;
  if (!("chatLimit" in value) || value["chatLimit"] === undefined) return false;
  if (
    !("autocompleteLimit" in value) ||
    value["autocompleteLimit"] === undefined
  )
    return false;
  return true;
}

export function GetFreeTrialStatus200ResponseFromJSON(
  json: any,
): GetFreeTrialStatus200Response {
  return GetFreeTrialStatus200ResponseFromJSONTyped(json, false);
}

export function GetFreeTrialStatus200ResponseFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): GetFreeTrialStatus200Response {
  if (json == null) {
    return json;
  }
  return {
    optedInToFreeTrial: json["optedInToFreeTrial"],
    chatCount: json["chatCount"] == null ? undefined : json["chatCount"],
    autocompleteCount:
      json["autocompleteCount"] == null ? undefined : json["autocompleteCount"],
    chatLimit: json["chatLimit"],
    autocompleteLimit: json["autocompleteLimit"],
  };
}

export function GetFreeTrialStatus200ResponseToJSON(
  json: any,
): GetFreeTrialStatus200Response {
  return GetFreeTrialStatus200ResponseToJSONTyped(json, false);
}

export function GetFreeTrialStatus200ResponseToJSONTyped(
  value?: GetFreeTrialStatus200Response | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    optedInToFreeTrial: value["optedInToFreeTrial"],
    chatCount: value["chatCount"],
    autocompleteCount: value["autocompleteCount"],
    chatLimit: value["chatLimit"],
    autocompleteLimit: value["autocompleteLimit"],
  };
}
