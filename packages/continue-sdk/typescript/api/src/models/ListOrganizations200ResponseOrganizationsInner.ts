/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface ListOrganizations200ResponseOrganizationsInner
 */
export interface ListOrganizations200ResponseOrganizationsInner {
  /**
   * Organization ID
   * @type {string}
   * @memberof ListOrganizations200ResponseOrganizationsInner
   */
  id: string;
  /**
   * Organization name
   * @type {string}
   * @memberof ListOrganizations200ResponseOrganizationsInner
   */
  name: string;
  /**
   * Pre-signed URL for the organization's icon
   * @type {string}
   * @memberof ListOrganizations200ResponseOrganizationsInner
   */
  iconUrl?: string | null;
  /**
   * Organization slug
   * @type {string}
   * @memberof ListOrganizations200ResponseOrganizationsInner
   */
  slug: string;
}

/**
 * Check if a given object implements the ListOrganizations200ResponseOrganizationsInner interface.
 */
export function instanceOfListOrganizations200ResponseOrganizationsInner(
  value: object,
): value is ListOrganizations200ResponseOrganizationsInner {
  if (!("id" in value) || value["id"] === undefined) return false;
  if (!("name" in value) || value["name"] === undefined) return false;
  if (!("slug" in value) || value["slug"] === undefined) return false;
  return true;
}

export function ListOrganizations200ResponseOrganizationsInnerFromJSON(
  json: any,
): ListOrganizations200ResponseOrganizationsInner {
  return ListOrganizations200ResponseOrganizationsInnerFromJSONTyped(
    json,
    false,
  );
}

export function ListOrganizations200ResponseOrganizationsInnerFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): ListOrganizations200ResponseOrganizationsInner {
  if (json == null) {
    return json;
  }
  return {
    id: json["id"],
    name: json["name"],
    iconUrl: json["iconUrl"] == null ? undefined : json["iconUrl"],
    slug: json["slug"],
  };
}

export function ListOrganizations200ResponseOrganizationsInnerToJSON(
  json: any,
): ListOrganizations200ResponseOrganizationsInner {
  return ListOrganizations200ResponseOrganizationsInnerToJSONTyped(json, false);
}

export function ListOrganizations200ResponseOrganizationsInnerToJSONTyped(
  value?: ListOrganizations200ResponseOrganizationsInner | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value["id"],
    name: value["name"],
    iconUrl: value["iconUrl"],
    slug: value["slug"],
  };
}
