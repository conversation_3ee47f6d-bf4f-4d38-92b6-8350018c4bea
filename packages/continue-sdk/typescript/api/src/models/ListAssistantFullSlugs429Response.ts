/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface ListAssistantFullSlugs429Response
 */
export interface ListAssistantFullSlugs429Response {
  /**
   *
   * @type {string}
   * @memberof ListAssistantFullSlugs429Response
   */
  message?: string;
}

/**
 * Check if a given object implements the ListAssistantFullSlugs429Response interface.
 */
export function instanceOfListAssistantFullSlugs429Response(
  value: object,
): value is ListAssistantFullSlugs429Response {
  return true;
}

export function ListAssistantFullSlugs429ResponseFromJSON(
  json: any,
): ListAssistantFullSlugs429Response {
  return ListAssistantFullSlugs429ResponseFromJSONTyped(json, false);
}

export function ListAssistantFullSlugs429ResponseFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): ListAssistantFullSlugs429Response {
  if (json == null) {
    return json;
  }
  return {
    message: json["message"] == null ? undefined : json["message"],
  };
}

export function ListAssistantFullSlugs429ResponseToJSON(
  json: any,
): ListAssistantFullSlugs429Response {
  return ListAssistantFullSlugs429ResponseToJSONTyped(json, false);
}

export function ListAssistantFullSlugs429ResponseToJSONTyped(
  value?: ListAssistantFullSlugs429Response | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    message: value["message"],
  };
}
