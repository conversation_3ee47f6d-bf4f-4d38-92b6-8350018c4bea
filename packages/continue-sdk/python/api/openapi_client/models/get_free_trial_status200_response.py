# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class GetFreeTrialStatus200Response(BaseModel):
    """
    GetFreeTrialStatus200Response
    """ # noqa: E501
    opted_in_to_free_trial: StrictBool = Field(description="Whether the user has opted into the free trial", alias="optedInToFreeTrial")
    chat_count: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Current number of chat messages used", alias="chatCount")
    autocomplete_count: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Current number of autocomplete requests used", alias="autocompleteCount")
    chat_limit: Union[StrictFloat, StrictInt] = Field(description="Maximum number of chat messages allowed in free trial", alias="chatLimit")
    autocomplete_limit: Union[StrictFloat, StrictInt] = Field(description="Maximum number of autocomplete requests allowed in free trial", alias="autocompleteLimit")
    __properties: ClassVar[List[str]] = ["optedInToFreeTrial", "chatCount", "autocompleteCount", "chatLimit", "autocompleteLimit"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of GetFreeTrialStatus200Response from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if chat_count (nullable) is None
        # and model_fields_set contains the field
        if self.chat_count is None and "chat_count" in self.model_fields_set:
            _dict['chatCount'] = None

        # set to None if autocomplete_count (nullable) is None
        # and model_fields_set contains the field
        if self.autocomplete_count is None and "autocomplete_count" in self.model_fields_set:
            _dict['autocompleteCount'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of GetFreeTrialStatus200Response from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "optedInToFreeTrial": obj.get("optedInToFreeTrial"),
            "chatCount": obj.get("chatCount"),
            "autocompleteCount": obj.get("autocompleteCount"),
            "chatLimit": obj.get("chatLimit"),
            "autocompleteLimit": obj.get("autocompleteLimit")
        })
        return _obj


