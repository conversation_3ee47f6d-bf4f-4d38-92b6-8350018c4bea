# ListAssistantFullSlugs429Response

## Properties

| Name        | Type    | Description | Notes      |
| ----------- | ------- | ----------- | ---------- |
| **message** | **str** |             | [optional] |

## Example

```python
from openapi_client.models.list_assistant_full_slugs429_response import ListAssistantFullSlugs429Response

# TODO update the JSON string below
json = "{}"
# create an instance of ListAssistantFullSlugs429Response from a JSON string
list_assistant_full_slugs429_response_instance = ListAssistantFullSlugs429Response.from_json(json)
# print the JSON string representation of the object
print(ListAssistantFullSlugs429Response.to_json())

# convert the object into a dict
list_assistant_full_slugs429_response_dict = list_assistant_full_slugs429_response_instance.to_dict()
# create an instance of ListAssistantFullSlugs429Response from a dict
list_assistant_full_slugs429_response_from_dict = ListAssistantFullSlugs429Response.from_dict(list_assistant_full_slugs429_response_dict)
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
