# GetAssistant404Response

## Properties

| Name        | Type    | Description | Notes      |
| ----------- | ------- | ----------- | ---------- |
| **message** | **str** |             | [optional] |

## Example

```python
from openapi_client.models.get_assistant404_response import GetAssistant404Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetAssistant404Response from a JSON string
get_assistant404_response_instance = GetAssistant404Response.from_json(json)
# print the JSON string representation of the object
print(GetAssistant404Response.to_json())

# convert the object into a dict
get_assistant404_response_dict = get_assistant404_response_instance.to_dict()
# create an instance of GetAssistant404Response from a dict
get_assistant404_response_from_dict = GetAssistant404Response.from_dict(get_assistant404_response_dict)
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
