# GetModelsAddOnCheckoutUrl200Response

## Properties

| Name    | Type    | Description                 | Notes |
| ------- | ------- | --------------------------- | ----- |
| **url** | **str** | Stripe checkout session URL |

## Example

```python
from openapi_client.models.get_models_add_on_checkout_url200_response import GetModelsAddOnCheckoutUrl200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetModelsAddOnCheckoutUrl200Response from a JSON string
get_models_add_on_checkout_url200_response_instance = GetModelsAddOnCheckoutUrl200Response.from_json(json)
# print the JSON string representation of the object
print(GetModelsAddOnCheckoutUrl200Response.to_json())

# convert the object into a dict
get_models_add_on_checkout_url200_response_dict = get_models_add_on_checkout_url200_response_instance.to_dict()
# create an instance of GetModelsAddOnCheckoutUrl200Response from a dict
get_models_add_on_checkout_url200_response_from_dict = GetModelsAddOnCheckoutUrl200Response.from_dict(get_models_add_on_checkout_url200_response_dict)
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
