# GetAssistant403Response

## Properties

| Name        | Type    | Description | Notes      |
| ----------- | ------- | ----------- | ---------- |
| **message** | **str** |             | [optional] |

## Example

```python
from openapi_client.models.get_assistant403_response import GetAssistant403Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetAssistant403Response from a JSON string
get_assistant403_response_instance = GetAssistant403Response.from_json(json)
# print the JSON string representation of the object
print(GetAssistant403Response.to_json())

# convert the object into a dict
get_assistant403_response_dict = get_assistant403_response_instance.to_dict()
# create an instance of GetAssistant403Response from a dict
get_assistant403_response_from_dict = GetAssistant403Response.from_dict(get_assistant403_response_dict)
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
