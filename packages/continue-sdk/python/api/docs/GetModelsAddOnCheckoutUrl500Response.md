# GetModelsAddOnCheckoutUrl500Response

## Properties

| Name        | Type    | Description | Notes      |
| ----------- | ------- | ----------- | ---------- |
| **message** | **str** |             | [optional] |

## Example

```python
from openapi_client.models.get_models_add_on_checkout_url500_response import GetModelsAddOnCheckoutUrl500Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetModelsAddOnCheckoutUrl500Response from a JSON string
get_models_add_on_checkout_url500_response_instance = GetModelsAddOnCheckoutUrl500Response.from_json(json)
# print the JSON string representation of the object
print(GetModelsAddOnCheckoutUrl500Response.to_json())

# convert the object into a dict
get_models_add_on_checkout_url500_response_dict = get_models_add_on_checkout_url500_response_instance.to_dict()
# create an instance of GetModelsAddOnCheckoutUrl500Response from a dict
get_models_add_on_checkout_url500_response_from_dict = GetModelsAddOnCheckoutUrl500Response.from_dict(get_models_add_on_checkout_url500_response_dict)
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
