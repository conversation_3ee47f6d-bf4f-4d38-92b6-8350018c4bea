# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.list_organizations200_response_organizations_inner import ListOrganizations200ResponseOrganizationsInner

class TestListOrganizations200ResponseOrganizationsInner(unittest.TestCase):
    """ListOrganizations200ResponseOrganizationsInner unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ListOrganizations200ResponseOrganizationsInner:
        """Test ListOrganizations200ResponseOrganizationsInner
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ListOrganizations200ResponseOrganizationsInner`
        """
        model = ListOrganizations200ResponseOrganizationsInner()
        if include_optional:
            return ListOrganizations200ResponseOrganizationsInner(
                id = '',
                name = '',
                icon_url = '',
                slug = ''
            )
        else:
            return ListOrganizations200ResponseOrganizationsInner(
                id = '',
                name = '',
                slug = '',
        )
        """

    def testListOrganizations200ResponseOrganizationsInner(self):
        """Test ListOrganizations200ResponseOrganizationsInner"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
