# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.sync_secrets_request import SyncSecretsRequest

class TestSyncSecretsRequest(unittest.TestCase):
    """SyncSecretsRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SyncSecretsRequest:
        """Test SyncSecretsRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SyncSecretsRequest`
        """
        model = SyncSecretsRequest()
        if include_optional:
            return SyncSecretsRequest(
                fqsns = [
                    None
                    ],
                org_scope_id = '',
                org_scope_slug = ''
            )
        else:
            return SyncSecretsRequest(
                fqsns = [
                    None
                    ],
        )
        """

    def testSyncSecretsRequest(self):
        """Test SyncSecretsRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
