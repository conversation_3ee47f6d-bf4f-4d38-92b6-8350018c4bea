# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.list_assistant_full_slugs429_response import ListAssistantFullSlugs429Response

class TestListAssistantFullSlugs429Response(unittest.TestCase):
    """ListAssistantFullSlugs429Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ListAssistantFullSlugs429Response:
        """Test ListAssistantFullSlugs429Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ListAssistantFullSlugs429Response`
        """
        model = ListAssistantFullSlugs429Response()
        if include_optional:
            return ListAssistantFullSlugs429Response(
                message = 'Too many requests'
            )
        else:
            return ListAssistantFullSlugs429Response(
        )
        """

    def testListAssistantFullSlugs429Response(self):
        """Test ListAssistantFullSlugs429Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
