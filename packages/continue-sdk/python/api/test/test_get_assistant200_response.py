# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.get_assistant200_response import GetAssistant200Response

class TestGetAssistant200Response(unittest.TestCase):
    """GetAssistant200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GetAssistant200Response:
        """Test GetAssistant200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GetAssistant200Response`
        """
        model = GetAssistant200Response()
        if include_optional:
            return GetAssistant200Response(
                config_result = openapi_client.models.list_assistants_200_response_inner_config_result.listAssistants_200_response_inner_configResult(
                    config = openapi_client.models.config.config(), 
                    config_load_interrupted = True, 
                    errors = [
                        ''
                        ], ),
                owner_slug = '',
                package_slug = '',
                icon_url = '',
                on_prem_proxy_url = '',
                use_on_prem_proxy = True,
                raw_yaml = ''
            )
        else:
            return GetAssistant200Response(
                config_result = openapi_client.models.list_assistants_200_response_inner_config_result.listAssistants_200_response_inner_configResult(
                    config = openapi_client.models.config.config(), 
                    config_load_interrupted = True, 
                    errors = [
                        ''
                        ], ),
                owner_slug = '',
                package_slug = '',
        )
        """

    def testGetAssistant200Response(self):
        """Test GetAssistant200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
