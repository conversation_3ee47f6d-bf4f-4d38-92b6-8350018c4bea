# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.get_assistant403_response import GetAssistant403Response

class TestGetAssistant403Response(unittest.TestCase):
    """GetAssistant403Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GetAssistant403Response:
        """Test GetAssistant403Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GetAssistant403Response`
        """
        model = GetAssistant403Response()
        if include_optional:
            return GetAssistant403Response(
                message = 'This assistant is not allowed in this organization'
            )
        else:
            return GetAssistant403Response(
        )
        """

    def testGetAssistant403Response(self):
        """Test GetAssistant403Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
