# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from openapi_client.models.get_free_trial_status200_response import GetFreeTrialStatus200Response

class TestGetFreeTrialStatus200Response(unittest.TestCase):
    """GetFreeTrialStatus200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GetFreeTrialStatus200Response:
        """Test GetFreeTrialStatus200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GetFreeTrialStatus200Response`
        """
        model = GetFreeTrialStatus200Response()
        if include_optional:
            return GetFreeTrialStatus200Response(
                opted_in_to_free_trial = True,
                chat_count = 1.337,
                autocomplete_count = 1.337,
                chat_limit = 1.337,
                autocomplete_limit = 1.337
            )
        else:
            return GetFreeTrialStatus200Response(
                opted_in_to_free_trial = True,
                chat_limit = 1.337,
                autocomplete_limit = 1.337,
        )
        """

    def testGetFreeTrialStatus200Response(self):
        """Test GetFreeTrialStatus200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
