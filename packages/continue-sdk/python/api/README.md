# openapi-client

API for Continue IDE to fetch assistants and other related information.
These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0
- Package version: 1.0.0
- Generator version: 7.12.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen
  For more information, please visit [https://continue.dev](https://continue.dev)

## Requirements.

Python 3.8+

## Installation & Usage

### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```

(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:

```python
import openapi_client
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```

(or `sudo python setup.py install` to install the package for all users)

Then import the package:

```python
import openapi_client
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import openapi_client
from openapi_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.continue.dev
# See configuration.py for a list of all supported configuration parameters.
configuration = openapi_client.Configuration(
    host = "https://api.continue.dev"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization: apiKeyAuth
configuration = openapi_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)


# Enter a context with an instance of the API client
with openapi_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = openapi_client.DefaultApi(api_client)
    owner_slug = 'owner_slug_example' # str | Slug of the user or organization that owns the assistant
    package_slug = 'package_slug_example' # str | Slug of the assistant package
    always_use_proxy = 'always_use_proxy_example' # str | Whether to always use the Continue-managed proxy for model requests (optional)
    organization_id = 'organization_id_example' # str | ID of the organization to scope assistants to. If not provided, personal assistants are returned. (optional)

    try:
        # Get a specific assistant by slug
        api_response = api_instance.get_assistant(owner_slug, package_slug, always_use_proxy=always_use_proxy, organization_id=organization_id)
        print("The response of DefaultApi->get_assistant:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling DefaultApi->get_assistant: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://api.continue.dev*

| Class        | Method                                                                                  | HTTP request                                         | Description                                       |
| ------------ | --------------------------------------------------------------------------------------- | ---------------------------------------------------- | ------------------------------------------------- |
| _DefaultApi_ | [**get_assistant**](docs/DefaultApi.md#get_assistant)                                   | **GET** /ide/get-assistant/{ownerSlug}/{packageSlug} | Get a specific assistant by slug                  |
| _DefaultApi_ | [**get_free_trial_status**](docs/DefaultApi.md#get_free_trial_status)                   | **GET** /ide/free-trial-status                       | Get free trial status for user                    |
| _DefaultApi_ | [**get_models_add_on_checkout_url**](docs/DefaultApi.md#get_models_add_on_checkout_url) | **GET** /ide/get-models-add-on-checkout-url          | Get Stripe checkout URL for models add-on         |
| _DefaultApi_ | [**get_policy**](docs/DefaultApi.md#get_policy)                                         | **GET** /ide/policy                                  | Get organization policy                           |
| _DefaultApi_ | [**list_assistant_full_slugs**](docs/DefaultApi.md#list_assistant_full_slugs)           | **GET** /ide/list-assistant-full-slugs               | List assistant full slugs (currently returns 429) |
| _DefaultApi_ | [**list_assistants**](docs/DefaultApi.md#list_assistants)                               | **GET** /ide/list-assistants                         | List assistants for IDE                           |
| _DefaultApi_ | [**list_organizations**](docs/DefaultApi.md#list_organizations)                         | **GET** /ide/list-organizations                      | List organizations for user                       |
| _DefaultApi_ | [**sync_secrets**](docs/DefaultApi.md#sync_secrets)                                     | **POST** /ide/sync-secrets                           | Synchronize secrets for user                      |

## Documentation For Models

- [GetAssistant200Response](docs/GetAssistant200Response.md)
- [GetAssistant403Response](docs/GetAssistant403Response.md)
- [GetAssistant404Response](docs/GetAssistant404Response.md)
- [GetFreeTrialStatus200Response](docs/GetFreeTrialStatus200Response.md)
- [GetModelsAddOnCheckoutUrl200Response](docs/GetModelsAddOnCheckoutUrl200Response.md)
- [GetModelsAddOnCheckoutUrl500Response](docs/GetModelsAddOnCheckoutUrl500Response.md)
- [GetPolicy200Response](docs/GetPolicy200Response.md)
- [ListAssistantFullSlugs429Response](docs/ListAssistantFullSlugs429Response.md)
- [ListAssistants200ResponseInner](docs/ListAssistants200ResponseInner.md)
- [ListAssistants200ResponseInnerConfigResult](docs/ListAssistants200ResponseInnerConfigResult.md)
- [ListAssistants401Response](docs/ListAssistants401Response.md)
- [ListAssistants404Response](docs/ListAssistants404Response.md)
- [ListOrganizations200Response](docs/ListOrganizations200Response.md)
- [ListOrganizations200ResponseOrganizationsInner](docs/ListOrganizations200ResponseOrganizationsInner.md)
- [SyncSecretsRequest](docs/SyncSecretsRequest.md)

<a id="documentation-for-authorization"></a>

## Documentation For Authorization

Authentication schemes defined for the API:
<a id="apiKeyAuth"></a>

### apiKeyAuth

- **Type**: Bearer authentication

## Author
