## [1.0.95](https://github.com/continuedev/continue/compare/@continuedev/config-yaml@1.0.94...@continuedev/config-yaml@1.0.95) (2025-06-22)

### Bug Fixes

- avoid minor ([7186c04](https://github.com/continuedev/continue/commit/7186c04418abd61f151d25bf03733f2f5d371b48))

# 1.0.0 (2025-06-22)

### Bug Fixes

- :adhesive_bandage: allow GGML to use api.openai.com ([db19f6b](https://github.com/continuedev/continue/commit/db19f6bc98285d8ea45b4db16f619dffbec7c3db))
- :adhesive_bandage: skip indexing really large files ([b773fe6](https://github.com/continuedev/continue/commit/b773fe6d7a0b489a658139ea5fc958abd46a20b2))
- :ambulance: catch error from meilisearch client.health ([00775f5](https://github.com/continuedev/continue/commit/00775f54e6c3fa8044a996ea1a7cf0f2205735dd))
- :ambulance: class_name hotfix ([83b0417](https://github.com/continuedev/continue/commit/83b0417f6c8c579d0ea5a0f689eceb822fe7a04d))
- :ambulance: fix import of run from **main** ([ebfe428](https://github.com/continuedev/continue/commit/ebfe428b7f70de66bc5692cca1db7cd10ef4b997))
- :ambulance: hotfix and package.json seo experiment ([42024ef](https://github.com/continuedev/continue/commit/42024effba73673b4080c25806c21293b5daad3e))
- :ambulance: load global ~/.continue/assistants ([4b55cfb](https://github.com/continuedev/continue/commit/4b55cfb2e8ad4803855020111d0cff6a38ad79d5))
- :ambulance: logging to file causing problems with starting server ([8b95ef7](https://github.com/continuedev/continue/commit/8b95ef7de258de8498b328d9e6107a95f57f8d2c))
- :ambulance: specify packagePath for vsix ([512ccfd](https://github.com/continuedev/continue/commit/512ccfda670abb6132e9cd720280a472e53e3326))
- :arrow_up: upgrade openai python package ([19cbe2c](https://github.com/continuedev/continue/commit/19cbe2cebae8e2155b6b4375c6a96a3b25e87615))
- :art: many small improvements ([28f5d7b](https://github.com/continuedev/continue/commit/28f5d7bedab05a8b061e4e7ee9055a5403786bbc))
- :bookmark: update extension version ([05b9642](https://github.com/continuedev/continue/commit/05b96420bda2da0c725cacc8141d87449eaf9e9c))
- :bookmark: update version ([afae160](https://github.com/continuedev/continue/commit/afae1600255714d0a4f18f892d3e7b5e1d921962))
- :bookmark: update version ([9aee2cc](https://github.com/continuedev/continue/commit/9aee2cc44c461ce0e001185af85352e78522bab5))
- :bookmark: update version to try again ([1905f31](https://github.com/continuedev/continue/commit/1905f319470c02ee414498b9101b6e64b4b15d65))
- :bookmark: v3 -> v4 of upload-artifact ([d24862a](https://github.com/continuedev/continue/commit/d24862a6fd22e4eac1b2ca27ce7bf029f0d8fa4a))
- :bug: a few minor fixes ([c918bb3](https://github.com/continuedev/continue/commit/c918bb3af5ec4a4a409eb0a3add27951b00c3c59))
- :bug: a handful of bug fixes ([e1325c0](https://github.com/continuedev/continue/commit/e1325c0153becb95b454810d9461efd7d3624a6a))
- :bug: a number of small fixes + disable summaries ([a975560](https://github.com/continuedev/continue/commit/a9755603c3a2c0b3afe809f77a63824c77c6419e))
- :bug: access highlighted_code through context_manager ([1afb37b](https://github.com/continuedev/continue/commit/1afb37b5bb901d95c493039591b9243cd2cdd6f7))
- :bug: add data file for ca_bundle ([b82d83f](https://github.com/continuedev/continue/commit/b82d83f79389897ed5f05eb9b5e8daf9cf64ee6f))
- :bug: Add requestOptions to YAML config for mcp ([81c20c1](https://github.com/continuedev/continue/commit/81c20c11dbbce2eccefd00364c5b74b298b2f24f))
- :bug: add server/exe to .vscodeignore insteading of manually removing ([e8ebff1](https://github.com/continuedev/continue/commit/e8ebff1e6b07dfaafff81ee7013bb019cbfe2075))
- :bug: additional fixes to ssh /edit ([4428acd](https://github.com/continuedev/continue/commit/4428acdd6f372c3724a908fafb1c793e0eae4096))
- :bug: allow end/home keys to work ([615d30e](https://github.com/continuedev/continue/commit/615d30e3dce92a9993b0e93b044faadf228529b1))
- :bug: allow None for timeout ([ff3de11](https://github.com/continuedev/continue/commit/ff3de1184737f1124090d384b877a30550b60869))
- :bug: another hotfix - don't destructure selectors ([534304a](https://github.com/continuedev/continue/commit/534304a2a4f9abfc221a961f279d1b43d14b6d33))
- :bug: another windows fix in typegen.js ([f38c8fb](https://github.com/continuedev/continue/commit/f38c8fb8b33a705ed4eb4d2e0974060ebb88afd3))
- :bug: async with Client (meilisearch) ([9a0cd64](https://github.com/continuedev/continue/commit/9a0cd644dcb5ff46817a6ea686a6de0fb764c960))
- :bug: attempting to fix mkdir ([c1a8097](https://github.com/continuedev/continue/commit/c1a8097f0a7f3cddb0aebac26e6197ffef186972))
- :bug: automigrate between short/long imports ([eecc2b5](https://github.com/continuedev/continue/commit/eecc2b57c5c5a144abfc0623102438e902c4aeba))
- :bug: avoid removing disallowed file windows ([19a3266](https://github.com/continuedev/continue/commit/19a3266b6f14186bd0839fac8b2a04b5a29f32e7))
- :bug: bug when highlighting code prior to context_manager creation ([74a52c8](https://github.com/continuedev/continue/commit/74a52c8399b3ccf2d2100b088b79e65c6ca6ad7e))
- :bug: bug where old server doesn't get updated ([bb776a0](https://github.com/continuedev/continue/commit/bb776a03df3e6a39a1726b781ea33c2ccebd5343))
- :bug: catch error when workspace uri isn't defined ([fc9eb30](https://github.com/continuedev/continue/commit/fc9eb3051fd5a7c9cad57b5d6cd93374bd8210fb))
- :bug: change for/backwardslash decoding scheme ([a3a05fe](https://github.com/continuedev/continue/commit/a3a05fee312ad7c04d2abb0e186da55c7d061462))
- :bug: chmod for linux as well as mac ([089def0](https://github.com/continuedev/continue/commit/089def08c58120f78df78c10027639802ad8f77d))
- :bug: clear all other selector destrucuring ([145642f](https://github.com/continuedev/continue/commit/145642f1eaf01d5809dabd79e7f64f234124683e))
- :bug: Codebase Indexing was not starting on load ([e391d58](https://github.com/continuedev/continue/commit/e391d583041c54edb3fa0836eb9186c61e6b063d))
- :bug: compatibility with python 3.8 ([275ad6f](https://github.com/continuedev/continue/commit/275ad6f72dafdfacffd9c9b5cc4847135a30f425))
- :bug: convert to correct path sep in wsl URIs ([1b2341a](https://github.com/continuedev/continue/commit/1b2341a0113fadf8c8d23097ef1041d3e3088e84))
- :bug: correct path sep for ssh-remote files ([b9bd8c1](https://github.com/continuedev/continue/commit/b9bd8c1848eaf38d5d15694a1ecae67f14566214))
- :bug: correction to ContinueConfig serialization model ([b8aba4b](https://github.com/continuedev/continue/commit/b8aba4bc96d3b064012a40d837d5191cae20037e))
- :bug: correctly generate uris for remote ([ab31cb1](https://github.com/continuedev/continue/commit/ab31cb15fae74592f49c2ceadc8d7810228fa7e2))
- :bug: ctrl+c for windows overriding copy ([c3925c0](https://github.com/continuedev/continue/commit/c3925c04d981d2abc1e21cf72d6e77d165420a73))
- :bug: custom escaping instead of URI for diff paths ([da3970e](https://github.com/continuedev/continue/commit/da3970e00061b7a223d23f51bd53012666d324dc))
- :bug: default to counting chars if tiktoken blocked ([7006dbb](https://github.com/continuedev/continue/commit/7006dbb3e38a837a2580a516791874f6815ac25f))
- :bug: don't fail on disconnected websocket ([0876610](https://github.com/continuedev/continue/commit/08766100cdb3638b3300ae4b700f8ec2af6b9a8a))
- :bug: don't log stdout to console ([ee4701d](https://github.com/continuedev/continue/commit/ee4701dc45cd540728302ca8a09e9b7ce842597f))
- :bug: don't open continue automatically ([8b76f51](https://github.com/continuedev/continue/commit/8b76f518313c20f13dda605931c9929ef58a7a22))
- :bug: don't override context length param in OpenAI ([b2a6d07](https://github.com/continuedev/continue/commit/b2a6d07ea99be1f9288ee21477edc0874e780cad))
- :bug: ebusy and logging bug fixes ([3d61469](https://github.com/continuedev/continue/commit/3d614690cd825ac5580074ecdc22f660455204f1))
- :bug: fix "code" keyerror prior to context_manager.start ([866b16c](https://github.com/continuedev/continue/commit/866b16c3a9c9d88a7b90aa8a43610fc4884ab123))
- :bug: fix /edit in ssh, pinyin input in combobox ([cda1be4](https://github.com/continuedev/continue/commit/cda1be46625abd8f44962cceeded04c8c47d9f65))
- :bug: fix >c_d.png file path ([a9bc4e2](https://github.com/continuedev/continue/commit/a9bc4e26263faef8598dd8aa2aec7949c75ab70c))
- :bug: fix 2 model config bugs ([b144d21](https://github.com/continuedev/continue/commit/b144d21b48a94aa8c203469eb7667bd22fc4e243))
- :bug: fix 404 from undefined gif ([b467371](https://github.com/continuedev/continue/commit/b4673712e1a6a5b435125004a9b51498207fb7b6))
- :bug: fix automigration ([ec41f55](https://github.com/continuedev/continue/commit/ec41f553c24d5f4b5bc4e601c989b1936d67ae1a))
- :bug: fix azure openai bug for 07 version ([a8e69a0](https://github.com/continuedev/continue/commit/a8e69a02e6897689a1727fb7542ed5684b1348e2))
- :bug: fix broken docs link ([210a02e](https://github.com/continuedev/continue/commit/210a02ef02341a98b4ed18095b2d656a7b994bd9))
- :bug: fix bugs when selecting code to edit ([fa34214](https://github.com/continuedev/continue/commit/fa34214012d14385d231a1ac4f16006aaf4331fb))
- :bug: fix ci to only upload from linux x64, not alpine ([4e1a5b1](https://github.com/continuedev/continue/commit/4e1a5b1fb3f96edc95b0938265da980e98566d56))
- :bug: fix cmd+m bug ([38e8272](https://github.com/continuedev/continue/commit/38e827243ceff3732cd0f260e7a3bd4941a96bc5))
- :bug: fix command enter, stop streaming on reject ([8e15ec3](https://github.com/continuedev/continue/commit/8e15ec3c2c1490d4a7d6371f877368376fd64e8a))
- :bug: fix command enter, stop streaming on reject ([19b3886](https://github.com/continuedev/continue/commit/19b38863c21656526e0729776682430e0fa277da))
- :bug: fix config.py import paths ([97861bf](https://github.com/continuedev/continue/commit/97861bf4117bbc36f8f87797a9ca60e6336f82cc))
- :bug: fix context length bug for /edit ([d103263](https://github.com/continuedev/continue/commit/d103263030ad52debe73bd131c71bbf17f545956))
- :bug: fix dialog links ([4c84e69](https://github.com/continuedev/continue/commit/4c84e6945a7c2018622eceb54e7fb54de193b03a))
- :bug: fix for --meilisearch-url flag ([e2798c5](https://github.com/continuedev/continue/commit/e2798c5bb62eeb2a3bc8f5baee18f9d64ee86563))
- :bug: fix for Azure OpenAI model names ([bcec2a0](https://github.com/continuedev/continue/commit/bcec2a0870d0ef649961b6c91ec866b612680b9e))
- :bug: fix for edit=None in highlightedCode update ([247d3e9](https://github.com/continuedev/continue/commit/247d3e9a41ff8d9fe2da6386bfb0d0eb063b071c))
- :bug: fix for lmstudio defaults ([0012922](https://github.com/continuedev/continue/commit/00129229cd881d6b910a4b01db68e702cdd63a40))
- :bug: fix for windows drive difference bug ([d69c6d4](https://github.com/continuedev/continue/commit/d69c6d4f3729374ab40fcebc861e67f2da100ad9))
- :bug: fix ggml bug ([1a75475](https://github.com/continuedev/continue/commit/1a75475c681053494984664ef1179171fe2a5d83))
- :bug: fix headers for openai.;y ([44fe0c9](https://github.com/continuedev/continue/commit/44fe0c94a55a753ff5d6c3da6b63db4a5c70d780))
- :bug: fix height bug after cmd+shift+R ([a7cb092](https://github.com/continuedev/continue/commit/a7cb0929bd064f73a1e3e49ba8dd6b6b7de387f4))
- :bug: fix history.timeline indexing bug ([01ed2c7](https://github.com/continuedev/continue/commit/01ed2c7eb2d3417b2c190eea105008372f49a7c6))
- :bug: fix huggingface tgi ([5316180](https://github.com/continuedev/continue/commit/5316180394d48d9877cda0cb3d7c3c6de9995d12))
- :bug: fix import in run.py ([4cf1f75](https://github.com/continuedev/continue/commit/4cf1f75518053f9df174d5ab90c426124f85ecfa))
- :bug: fix inability to copy/paste when ipynb is open ([850c8ae](https://github.com/continuedev/continue/commit/850c8aea7f3d9c46ff8e98bde936b92282376dae))
- :bug: fix incorrect imports in default config file ([374bdd0](https://github.com/continuedev/continue/commit/374bdd037792825bf984026da12d4100ffebcac2))
- :bug: fix keyboard shortcut for debugging ([c021958](https://github.com/continuedev/continue/commit/c021958ae893a9683352ba99e5c6301e38331492))
- :bug: fix meilisearch empty body content-type bug ([598e243](https://github.com/continuedev/continue/commit/598e243fd292dd8851865ab1c3915ca55f4992cc))
- :bug: fix missing path import ([5bfe68e](https://github.com/continuedev/continue/commit/5bfe68ea7f7e90e3cb1c3101360cf959b336a857))
- :bug: fix model changing bug ([fd4a4dc](https://github.com/continuedev/continue/commit/fd4a4dcf004bea86d982ffffb66b4e3cb38193a6))
- :bug: fix overriding of system message ([8444e76](https://github.com/continuedev/continue/commit/8444e76b7232fbddb62d3626de13653ae332d168))
- :bug: fix paths ([b893c95](https://github.com/continuedev/continue/commit/b893c956fe75a9e45f06129290d043737f5c1007))
- :bug: fix reducers for user input queue ([1a36a3c](https://github.com/continuedev/continue/commit/1a36a3c02acaf6bf29d4153c113217517b832942))
- :bug: fix replicate to work with models requiring prompt input ([84ec574](https://github.com/continuedev/continue/commit/84ec574e182ec441e95d13c3543a934e0a036228))
- :bug: fix serialization bug for context_providers ([2799249](https://github.com/continuedev/continue/commit/27992499af977baeb9124d9ab35ffec6d36a298a))
- :bug: fix set_system_message ([084fdac](https://github.com/continuedev/continue/commit/084fdac3992f58dcf11241e7e5c2d5efa784ce0d))
- :bug: fix ssh /edit by checking for file through vscode fs ([417d45c](https://github.com/continuedev/continue/commit/417d45ccddc2f434d7467e4f17113783996653dd))
- :bug: fix telemetry bug ([042bc5a](https://github.com/continuedev/continue/commit/042bc5ac76800ee66e603ef23b2bb857fafe053e))
- :bug: Fix the generating animation ([2f402b4](https://github.com/continuedev/continue/commit/2f402b4a1227ade5a4ba70f770974627b586e930))
- :bug: fix timeout type ([e1a0290](https://github.com/continuedev/continue/commit/e1a0290d5a699e30464f1e682cb11c6aa119bd59))
- :bug: fix togetherAI model json parsing ([deb291c](https://github.com/continuedev/continue/commit/deb291c1b225425cba543dd3b4c5557089abfb59))
- :bug: fix undefined.filter bug ([9b58278](https://github.com/continuedev/continue/commit/9b582781ab0aceaaf1cff7432fed92fa6c205aae))
- :bug: fix usages of LLM.complete ([f057ee4](https://github.com/continuedev/continue/commit/f057ee4d619b834dc245065d13417a86b44dc61b))
- :bug: fix when multiple cursor ranges are selected ([f9c145c](https://github.com/continuedev/continue/commit/f9c145c9667e0cd9adb7f9b645f7abf12f7cf2a2))
- :bug: fix yaml syntax error ([11c7cec](https://github.com/continuedev/continue/commit/11c7cecc107ef9f2571926055dbd80495fe0f8b2))
- :bug: fixes for a few context_providers ([41b3233](https://github.com/continuedev/continue/commit/41b3233693c34cd81c872a1e7279721b5f640d60))
- :bug: fixes to templating messages ([c56e24d](https://github.com/continuedev/continue/commit/c56e24d2a5f2b40702e4b495fa3f28d554eaa3ab))
- :bug: fixing bugs with ggml ([87409c3](https://github.com/continuedev/continue/commit/87409c31832ccb707abbf134843323c9eb6e1183))
- :bug: fixing issues with creating markdown files ([5c1c2d6](https://github.com/continuedev/continue/commit/5c1c2d626ffed786d00c79aadef26fa5718ca43d))
- :bug: fixing small UI details ([088b7b8](https://github.com/continuedev/continue/commit/088b7b803866817aaedce6b61834f1ce5de7a7c2))
- :bug: force kill old server with taskkill on windows ([b1b7d13](https://github.com/continuedev/continue/commit/b1b7d13dbf5b9f6ada28a5ef22ea6857d3b0bcb6))
- :bug: ftc fix ([f5f10ef](https://github.com/continuedev/continue/commit/f5f10efee3402e117c34b6f0de4bf2fd7d2819c1))
- :bug: gpt-4-32k in CHAT_MODELS ([b0445cd](https://github.com/continuedev/continue/commit/b0445cd5fc4538c8a9c4f3e76be0f3d724c99818))
- :bug: handle when vscode workspace not open ([73c6827](https://github.com/continuedev/continue/commit/73c6827d02ff62313184e3745fd94c7591c98b61))
- :bug: hotfix for user_input_queue.map ([610c576](https://github.com/continuedev/continue/commit/610c576cc9df72716c5e65838f805b15431011ea))
- :bug: install python-virtualenv on linux, fix git hash files error ([6f0e634](https://github.com/continuedev/continue/commit/6f0e6340bb22ee150ef4b7996750f4c63c0bc2a7))
- :bug: install python-virtualenv on linux, fix git hash files error ([7fa98ff](https://github.com/continuedev/continue/commit/7fa98ffe843320ddc63794a497a2d44570e005c3))
- :bug: kill server before trying to delete exe on windows ([286fb0e](https://github.com/continuedev/continue/commit/286fb0e20e48859f129ccf568d03248805bcbc61))
- :bug: let context providers work without meilisearch ([0f86a69](https://github.com/continuedev/continue/commit/0f86a69e4a83458db2e20e404c26dac2e02355cf))
- :bug: llamacpp fix indexing max_tokens ([90590ab](https://github.com/continuedev/continue/commit/90590ab4e06fbc3fa721f73a4a922136946a756f))
- :bug: make sure server_version.txt exists ([17806d9](https://github.com/continuedev/continue/commit/17806d932502adbf974ccd93a670e57b78be9a08))
- :bug: make typegen.js windows compatible ([dc06228](https://github.com/continuedev/continue/commit/dc0622848b648ba27e7110b9b900673bb668ab4c))
- :bug: MAX_TOKENS_FOR_MODEL bug fix, more testing ([1c288f7](https://github.com/continuedev/continue/commit/1c288f7749747c6b1908ae16c977f80e5597d2ca))
- :bug: meilisearch fixes ([0de6e19](https://github.com/continuedev/continue/commit/0de6e1985d0e97ede5e19e7752a6be7cd2a5818d))
- :bug: more reliable download with request ([fdb036b](https://github.com/continuedev/continue/commit/fdb036bcecec891adaf99d73101c458fc4087406))
- :bug: more reliable setup of meilisearch ([12a8ae1](https://github.com/continuedev/continue/commit/12a8ae1c47f111b9f36633c96b26e8642c5ff223))
- :bug: now progress bar when api_key entered ([bf82c6f](https://github.com/continuedev/continue/commit/bf82c6fd16a6777f0a9bb68ce4879d7bab9019bb))
- :bug: number of bug fixes ([b9bdf58](https://github.com/continuedev/continue/commit/b9bdf5894c1c68b60d1919ae07b0f5909b00dec2))
- :bug: numerous small fixes ([0940d75](https://github.com/continuedev/continue/commit/0940d756dec3b98071ae5e5a12966e02420b3cd2))
- :bug: patch for ocassional 0 choices from older azure versions ([5c09b80](https://github.com/continuedev/continue/commit/5c09b8077588a447d6eaac9b7f624571be3ddb1d))
- :bug: permissions for pypi-deployment step ([b237850](https://github.com/continuedev/continue/commit/b237850c4b64435e26dfb5f12275a16a93e556a8))
- :bug: post-merge fixes ([96379a7](https://github.com/continuedev/continue/commit/96379a7bf5b576a2338142b10932d98cbc865d59))
- :bug: remove empty grammar from llama_cpp_args ([e5bbe3b](https://github.com/continuedev/continue/commit/e5bbe3bc4d59b6f35db1ce1b94be14244c11c766))
- :bug: replace hardcoded path for config file ([4fe9ace](https://github.com/continuedev/continue/commit/4fe9ace518bcdcf79999ce9938ba01b218d355e4))
- :bug: require socksio ([bba5e5e](https://github.com/continuedev/continue/commit/bba5e5e5b1da2dd924aa2632e38d4bb702bbbdd9))
- :bug: separately load ctx provs, fix filetree ([d8e821e](https://github.com/continuedev/continue/commit/d8e821e422678fd4248b472c7f3e67a32ecfefb5))
- :bug: set api_keys in config.py, fix spawn error handling ([6823307](https://github.com/continuedev/continue/commit/68233071dd0d97a353a66fe5627d69f97a389ca8))
- :bug: set export display in same step as linux npm test ([c3d62c5](https://github.com/continuedev/continue/commit/c3d62c5ae203aaca32583f75a7e80dfd9f196e11))
- :bug: small bug fix ([32d1149](https://github.com/continuedev/continue/commit/32d1149692c26eb966693f03db6d9cf496ba57a4))
- :bug: small bug fixes ([bc75ff2](https://github.com/continuedev/continue/commit/bc75ff294a2b5ec5eef5f77aff72aaa0c7f4a3f2))
- :bug: small fixes, update troubleshooting docs ([51fc07c](https://github.com/continuedev/continue/commit/51fc07cf6441d6330ce64e45e56e8f333ca309ed))
- :bug: solve EBUSY by polling ([9417973](https://github.com/continuedev/continue/commit/941797359f6554ac16a2e478047aabd5cbc0404b))
- :bug: ssh compatibility by reading from vscode.workspace.fs ([e5f5630](https://github.com/continuedev/continue/commit/e5f56308c5fd87695278682b2a36ca60df0db863))
- :bug: start meilisearch in parallel to server ([e4c1bb4](https://github.com/continuedev/continue/commit/e4c1bb4bedbe426d090f4bb2b8819ad935c5b3fb))
- :bug: stop streaming on rejection ([8d05fc2](https://github.com/continuedev/continue/commit/8d05fc2bb5c5df617800c1abcf43bb03c574482f))
- :bug: stop streaming on rejection ([9fc831e](https://github.com/continuedev/continue/commit/9fc831e7587cce99c8a6f2e56905c25068c8cab6))
- :bug: streaming url_decode for Ollama ([3690101](https://github.com/continuedev/continue/commit/3690101b790f91c749f208693aaffc00b9fa2a42))
- :bug: templating fix for queued LLM ([5c6609a](https://github.com/continuedev/continue/commit/5c6609ab5fa3a69cd0e3e8e61df643fcce1ecb47))
- :bug: temporarily disable lsp before fixing w/ vscode ([98f340b](https://github.com/continuedev/continue/commit/98f340bd97cba6f30cfe55d47419e3925b9dc679))
- :bug: temporarily remove replicate altogether ([bd79c00](https://github.com/continuedev/continue/commit/bd79c00e7790b92cfd8b8c8f8211b6c3d36e33a2))
- :bug: test and fix small issues with GGML ([72e8332](https://github.com/continuedev/continue/commit/72e83325a8eb5032c448a5e891c157987921ced2))
- :bug: timeout on blocking processes ([345b773](https://github.com/continuedev/continue/commit/345b7734d8c887d699d5038416d2a1f8193a33e9))
- :bug: traceback fixes, remove replicate from hiddenimports ([45d9bab](https://github.com/continuedev/continue/commit/45d9bab5cea745573be7112d7130089c596c88fa))
- :bug: try/except around starting meilisearch ([c867cd4](https://github.com/continuedev/continue/commit/c867cd40342d44901cf5277ded25f5dc5aaa4326))
- :bug: update search path for ripgrep on windows ([e428dc5](https://github.com/continuedev/continue/commit/e428dc53cedf54f394a7cddfe8a7ce7fbf469bb9))
- :bug: update the tip message for keyboard shortcut ([3558450](https://github.com/continuedev/continue/commit/355845002e178a618e9a792dd57b0649c3da8845))
- :bug: urldecode ollama responses, make edit faster ([19050f8](https://github.com/continuedev/continue/commit/19050f83228b3e7f08a6aacd5bdd1804a8315e4a))
- :bug: use certifi to set ca_bundle_path for openai ([3849420](https://github.com/continuedev/continue/commit/3849420948e491d5f84ac485169165d887751fd3))
- :bug: use posthog-node, not -js ([88a8166](https://github.com/continuedev/continue/commit/88a8166476d38889fd4f9323472cc34a5226e05c))
- :bug: use powershell remove-item ([64552dc](https://github.com/continuedev/continue/commit/64552dc881509c46aa14253ff94aee9d86ade256))
- :bug: use windows equivalent of rm -rf ([8d19866](https://github.com/continuedev/continue/commit/8d198663e116c7c77b7e59015bc6032736f71f6e))
- :bug: verify_ssl and ssl_context mutual exclusivity ([59b7453](https://github.com/continuedev/continue/commit/59b7453afed06418d4c171b65370a6a82f5a9221))
- :bug: version patch in the publish step ([1936f72](https://github.com/continuedev/continue/commit/1936f725d226bea2e13d5d88c1dd7a9a02ddd259))
- :bug: windowsHide on process spawn ([c3d31f0](https://github.com/continuedev/continue/commit/c3d31f00bb589df1c83308b7d9d69ed51c31341a))
- :bug: write out npm run package as package.js ([4636c95](https://github.com/continuedev/continue/commit/4636c9590154d6b5995948003da212eb25003750))
- :bug: write to local diff files ([6140d05](https://github.com/continuedev/continue/commit/6140d05e7d415d3334032c300ed593bdd181f7f5))
- :children_crossing: add slash commands to default config ([58e5dc4](https://github.com/continuedev/continue/commit/58e5dc4a5c4fcbed25170b61fbd88d479c5aebcf))
- :children_crossing: clear the dropdown after text input cleared ([23167a5](https://github.com/continuedev/continue/commit/23167a51d959fed5e4be057ceb9fff50cf34c6c8))
- :children_crossing: don't order meilisearch results by contnet ([ab7a90a](https://github.com/continuedev/continue/commit/ab7a90a0972188dcc7b8c28b1263c918776ca19d))
- :children_crossing: use default model in default config.py ([1bc5777](https://github.com/continuedev/continue/commit/1bc5777ed168e47e2ef2ab1b33eecf6cbd170a61))
- :construction_worker: copy_metadata for replicate in run.spec ([b0426d8](https://github.com/continuedev/continue/commit/b0426d82a4871e9081367ad4e977b22f42db5a89))
- :construction: working on fixing lsp ([1f95bb2](https://github.com/continuedev/continue/commit/1f95bb287846fc0501193d642420b574d9900857))
- :fire: remove version from package.json ([27c0a40](https://github.com/continuedev/continue/commit/27c0a403de28345cd03c39ad46c02f68ff57b3a1))
- :goal_net: catch errors when loading to meilisearch index ([7894c8e](https://github.com/continuedev/continue/commit/7894c8ed1517394aa00f6e496a97d9e27d204f5f))
- :goal_net: display errors in SimpleChatStep ([72784f6](https://github.com/continuedev/continue/commit/72784f6f1161f0c5b647889c26089a8247111dc9))
- :green_heart: cd extension before packaging ([0a6d72c](https://github.com/continuedev/continue/commit/0a6d72c099316e6cffa123d3ffa915f3fe13e770))
- :green_heart: cleanup file ([951552d](https://github.com/continuedev/continue/commit/951552dd0eede1f8f255aeaf5d34a13ff0c7bfb7))
- :green_heart: don't exclude jedi from pyinstaller ([a1328cb](https://github.com/continuedev/continue/commit/a1328cb5431f99cfe16b246ee4201b19530404e2))
- :green_heart: fix build scripts ([ab799b0](https://github.com/continuedev/continue/commit/ab799b0b0133e926ea06a1a12c092f42b9e053a1))
- :green_heart: fix copy statement to include.exe for windows ([36c3dfd](https://github.com/continuedev/continue/commit/36c3dfd51d319b9b9ad392988d13ef7f443e0937))
- :green_heart: fix preview.yaml ([a736d62](https://github.com/continuedev/continue/commit/a736d62f0e8b9b80ab9a949fd1739fb9a3be26e1))
- :green_heart: increase testing timeout to allow for fkill ([08b1cfd](https://github.com/continuedev/continue/commit/08b1cfdd2f6f456df7344c16f5d229a0ccfb841b))
- :green_heart: install rosetta ([5b9ef10](https://github.com/continuedev/continue/commit/5b9ef102973c608bc409a7b9ec244a4be1494e96))
- :green_heart: one last test ([758520f](https://github.com/continuedev/continue/commit/758520fe6b59d3330dec80ac07d05282d36e0058))
- :green_heart: only upload once per binary ([490838a](https://github.com/continuedev/continue/commit/490838a8ad920a52ada7e85675aefd965e978d77))
- :green_heart: package patch ([34f32ed](https://github.com/continuedev/continue/commit/34f32ed5f71055ea11d4332f18e77ceba5849631))
- :green_heart: package:pre-release ([d3f21da](https://github.com/continuedev/continue/commit/d3f21da803e36b78f968c2216d9f93f90ebabd6a))
- :green_heart: publish as pre-release! ([831bf5e](https://github.com/continuedev/continue/commit/831bf5e7f7c48dd80f71f1256f5597bd47bf22de))
- :green_heart: publishing to depend on ALL tests ([a131c17](https://github.com/continuedev/continue/commit/a131c17326591e67a68faf6f96371ad8fc332b71))
- :green_heart: pull origin main in main.yaml after pypi update ([6a9a079](https://github.com/continuedev/continue/commit/6a9a079914d94419183182cd0a5cc4439f2101ad))
- :green_heart: remove "patch" from vsce publish command ([d8327ec](https://github.com/continuedev/continue/commit/d8327ec6f82058479bd294bfcdccaf3c2b54de0a))
- :green_heart: remove npm_config_arch ([8c51664](https://github.com/continuedev/continue/commit/8c5166471b0d83b924d8bee1e0ca51822cc1bbdc))
- :green_heart: remove version from apckage.json ([5438ce9](https://github.com/continuedev/continue/commit/5438ce94406baa0f7d131ecacadefc72912dca0d))
- :green_heart: set permissions on apple silicon binary ([715cfed](https://github.com/continuedev/continue/commit/715cfed18747b6bc2e6d7bd7a977d249cc9066d5))
- :green_heart: testing ([14062c5](https://github.com/continuedev/continue/commit/14062c5c385bb7ba80096bf7daf6b6a5568b0b54))
- :green_heart: testing for failure to package dist in vsix ([19acf3b](https://github.com/continuedev/continue/commit/19acf3bb36c1e44274297c806b89b589ca02f5ba))
- :green_heart: update permissions and version ([c4ed41c](https://github.com/continuedev/continue/commit/c4ed41c861573f4c9bdff1a21ca3e056cfdd766e))
- :green_heart: update pylsp hidden import ([1e8ea65](https://github.com/continuedev/continue/commit/1e8ea654f5ad1e06bff2660b54a50955098703ba))
- :green_heart: update pypi version, don't push from main ([208eb65](https://github.com/continuedev/continue/commit/208eb65f67ccc62ce6d683fd9bed2fe9524b2136))
- :green_heart: use curl to download binary ([199e4b3](https://github.com/continuedev/continue/commit/199e4b3b99642ba5b1558132aa10119be1eeb525))
- :heavy_plus_sign: add bs4 to requirements.txt ([8a1e6fb](https://github.com/continuedev/continue/commit/8a1e6fb4adec6e5febb2a0d78eb0b2a01bfa028b))
- :heavy_plus_sign: add ripgrepy dependency to requirements.txt ([9801b50](https://github.com/continuedev/continue/commit/9801b50192ca661972d5b2997028db3cd0725fb7))
- :heavy_plus_sign: hidden import for replicate ([b75555f](https://github.com/continuedev/continue/commit/b75555f106be3c7612e7c31818ff674485096e4f))
- :heavy_plus_sign: include replicate in requirements.rtxt ([01b3f1f](https://github.com/continuedev/continue/commit/01b3f1ff8f4dd89ae79f15626ef5a3af2bc558c4))
- :lipstick: don't display entirety of large tracebacks ([a74eda5](https://github.com/continuedev/continue/commit/a74eda56cfcafb5c463a74df564ced6f882f8d3e))
- :lipstick: fix layout bugs ([b655781](https://github.com/continuedev/continue/commit/b6557810d70a7f341761d5018fa2835cc3a50af1))
- :lipstick: fix UI problems in vscode light themes ([5e8866d](https://github.com/continuedev/continue/commit/5e8866da83f8a97cb8492f26e175b948d0282262))
- :lipstick: logo alignment, better config failure description, patch ([c51ad53](https://github.com/continuedev/continue/commit/c51ad538deff06af6c9e5498b23e3536e18bfc4c))
- :lipstick: nicer autoscroll ([88699ff](https://github.com/continuedev/continue/commit/88699ff909b026511da392bf2c0a96be02abc6fd))
- :lipstick: small UI improvements ([ec4fb4d](https://github.com/continuedev/continue/commit/ec4fb4d9235151901c1f7367932ecc17ab55d8e4))
- :lipstick: ui tweaks to history + scrollbars ([6e8885f](https://github.com/continuedev/continue/commit/6e8885fc2f7feb06ef6ac87d2d7688f9f33d15de))
- :lipstick: update font size for input, remove first tutorial step ([73ff267](https://github.com/continuedev/continue/commit/73ff2678ad984c9d9082ec078a38450d5daa1376))
- :lipstick: update light gray hex code ([e0e0482](https://github.com/continuedev/continue/commit/e0e0482f2af2eadd3df72fbdb6974c07ba11c527))
- :lock: opt out of meilisearch analytics ([8db5b39](https://github.com/continuedev/continue/commit/8db5b39170229ba93b83f526e7fd80056e461c6a))
- :loud_sound: better logging for ggml completion endpoint ([0459b0c](https://github.com/continuedev/continue/commit/0459b0c919903852254ac2cd081307788884cd84))
- :loud_sound: fix logs to be sent from uvicorn ([d3b4103](https://github.com/continuedev/continue/commit/d3b4103cd2f639fc072b8a3269d7730478c8bb1c))
- :loud_sound: websocket logging and horizontal scrollbar ([7bb0fe3](https://github.com/continuedev/continue/commit/7bb0fe34bbc8affce0c675b88ffb79a6b9985860))
- :memo: escape <QUESTION> in docs ([7314e79](https://github.com/continuedev/continue/commit/7314e79ac5bc34936a2c3de0fd01aadbfe640e72))
- :memo: fix deployent readme ([7a38025](https://github.com/continuedev/continue/commit/7a3802523c2e5ae136c39849e2fbb0d3e7bba63e))
- :memo: remove reference duplicates for ctx providers ([043d695](https://github.com/continuedev/continue/commit/043d695198caed305fa6651918c3bbb2de87db36))
- :memo: small fix in troubleshooting.md ([275a03b](https://github.com/continuedev/continue/commit/275a03b7f1e32f57bd68e501074aa80e0dbed40f))
- :memo: use backup server links in docs ([815627b](https://github.com/continuedev/continue/commit/815627b167e4bf06308b51c6756e33c36b17b631))
- :pencil2: Fix typo that was causing automatic version bumping not to work for intellij ([3daf2c7](https://github.com/continuedev/continue/commit/3daf2c7b23caf838b862c2d2791ae8655b761d12))
- :rocket: fallback s3 bucket ([aa98080](https://github.com/continuedev/continue/commit/aa98080cb16c75d2b7d6d9771b97e63120052c62))
- :safety_vest: more safely convert windows path to posix ([4309f9d](https://github.com/continuedev/continue/commit/4309f9def89c25611273d99db01e7cc477ad935e))
- :white_check_mark: allow longer for python server to start in test ([d8f5f10](https://github.com/continuedev/continue/commit/d8f5f102f6f91487be0281316e581858ec4ca260))
- :white_check_mark: allow longer wait in test ([40ec1a3](https://github.com/continuedev/continue/commit/40ec1a31a7cd37da8b75bbabf1f0d160bb7bec5d))
- :zap: register vscode commands prior to server loading ([f7a3659](https://github.com/continuedev/continue/commit/f7a3659381f839b890f2c53086f7fedecf23d9ab))
- :zap: update count_tokens method ([8214203](https://github.com/continuedev/continue/commit/82142033f935d6236620d82e31a70ea8f2fb243e))
- 'inferenceConfig.stopSequences' failed to satisfy constraint: Member must have length less than or equal to 4 [#2538](https://github.com/continuedev/continue/issues/2538) ([90e994d](https://github.com/continuedev/continue/commit/90e994db3c106f9b63bd043203111e5e101071ba))
- (very) small typo breaking the prompt file examples link! ([bd6f9b9](https://github.com/continuedev/continue/commit/bd6f9b969647ee8cf6138dca63538063414553bb))
- `REPLACE INTO` code_snippet table ([bdb967d](https://github.com/continuedev/continue/commit/bdb967d7b3c18fa2e0237f3cc0e0ea7415102715))
- 🐛 Codebase Indexing still not work ([955ab93](https://github.com/continuedev/continue/commit/955ab93efc3f488127d16673b89f3900f75c2007))
- 🐛 typo in core.py ([#429](https://github.com/continuedev/continue/issues/429)) ([705324e](https://github.com/continuedev/continue/commit/705324ed2ef588b2885c0b03107b9e30ae358dae))
- a bunch of bugs, commit residuals such as npm install pg ([6d74e6b](https://github.com/continuedev/continue/commit/6d74e6b1fedf18b11ea12ba38164fcd146fdba4b))
- actions ([653ca20](https://github.com/continuedev/continue/commit/653ca205dfbe2f7fec94891c99053d393d3efd0c))
- add .mvn/ to list of default ignored folders ([ec91021](https://github.com/continuedev/continue/commit/ec91021adfcbd49becc3fdfbee6784981238a9f4))
- add 'rich' module to requirements.txt ([#612](https://github.com/continuedev/continue/issues/612)) ([5d21bdf](https://github.com/continuedev/continue/commit/5d21bdf2930b30723f1fd80b05d8c1c2ad589bb2))
- add checkmark icon to indicate selected model in dropdown ([98a3219](https://github.com/continuedev/continue/commit/98a321939ea1ba551025b16dc09f13e7a5e980ca))
- add code range for quick actions/fixes ([#1687](https://github.com/continuedev/continue/issues/1687)) ([9f2e9bc](https://github.com/continuedev/continue/commit/9f2e9bc2dff474447d8502e386bb0cc804730bb9))
- add context provider ([ae888d9](https://github.com/continuedev/continue/commit/ae888d9e814cf10931222070ed1e2ae5438325fe))
- Add context/getSymbolsForFiles endpoint in JetBrains and handle symbol retrieval errors ([ba1fdf7](https://github.com/continuedev/continue/commit/ba1fdf7bac07afe04e62bd659a87dc89800a302b))
- Add ContinuePluginDisposable to avoid memory leaks in Jetbrains ([a48a150](https://github.com/continuedev/continue/commit/a48a150b2024fca63cb50fb09a914a391c0cfce5))
- add Delphi/Pascal syntax highlighting support ([cfcb06b](https://github.com/continuedev/continue/commit/cfcb06b1763b17e9e6d150963e090523c372d5d6))
- Add directory checks and optimize token usage ([79736c3](https://github.com/continuedev/continue/commit/79736c3108313204184bb7d833ac21e23341354f))
- add focus to InputToolbar on click ([#703](https://github.com/continuedev/continue/issues/703)) ([6b17de4](https://github.com/continuedev/continue/commit/6b17de49ed0e991221baee986f9dbb758d55f291))
- add hover effect and restrict clickable area for the history and more back buttons ([9662b18](https://github.com/continuedev/continue/commit/9662b18a1ff36d8eaf743003f4df501245a31be2))
- add missing eos_token for qwen2.5-coder ([6a7eea2](https://github.com/continuedev/continue/commit/6a7eea27ca5d1ade790ce08dc1cf4eb7bbdeb585))
- add missing import ([400be9b](https://github.com/continuedev/continue/commit/400be9b47f0b8ab52b4e32ca77339497c63c8f96))
- add mistral model options to config schema ([d56b48c](https://github.com/continuedev/continue/commit/d56b48cad2fcb0bb8e4a3e0dd3e32ab7f704d762))
- add Msty logo ([397dcf4](https://github.com/continuedev/continue/commit/397dcf4ccc663560a49ff41e5892a5c118bce02e))
- add new UI breakpoints ([5105645](https://github.com/continuedev/continue/commit/5105645dedec92abd3fc613777f60fd2a638a0ab))
- add unit tests for commandExtractor and improve multiline comment handling ([0d940ec](https://github.com/continuedev/continue/commit/0d940ec6d1377b38945bd6217040cd025f89a80c))
- add uuid to chat msgs for deletions ([f63fd5d](https://github.com/continuedev/continue/commit/f63fd5d83cefbbe01721515a8f28f435353bd804))
- addTag ([53f886f](https://github.com/continuedev/continue/commit/53f886fd40c7bca29de06156f0e713a9700cc189))
- allow downloading missing embedder on Ollama, in VS Code ([1dc58c6](https://github.com/continuedev/continue/commit/1dc58c63ab174f579e860d778ae85e73f6fb8c9d))
- always show close icon ([4faed0b](https://github.com/continuedev/continue/commit/4faed0bc17a2b79e24fe5133efc40868bccaa6aa))
- apply actions in toolbar ([cd810eb](https://github.com/continuedev/continue/commit/cd810eb93939bccc5bc0ff1e5c54572c7d27a97d))
- apply insertion bug ([ceb9277](https://github.com/continuedev/continue/commit/ceb92773e9a6e4da27aa2de8f871c07f1e23d1a7))
- apply notch filename trunaction ([033ade0](https://github.com/continuedev/continue/commit/033ade024ed685ef66dad53e675449a47e2dc872))
- artifact name ([85e3fbc](https://github.com/continuedev/continue/commit/85e3fbc1c73872b1dfd8aee1f1dcef26b9f37761))
- artifact upload ([1039e77](https://github.com/continuedev/continue/commit/1039e777c8a081ed9362f29e20c7c6133f3b07cd))
- attempt to fix [#485](https://github.com/continuedev/continue/issues/485) ([#498](https://github.com/continuedev/continue/issues/498)) ([1188dd7](https://github.com/continuedev/continue/commit/1188dd7e5f26ed57d034c927ba032739963b9abc))
- auth ([72636d9](https://github.com/continuedev/continue/commit/72636d96c8d8bb303e66f2344f5eeef26f078816))
- autocomplete label after selecting a query type context provider ([#1562](https://github.com/continuedev/continue/issues/1562)) ([6407458](https://github.com/continuedev/continue/commit/6407458d7626d52558ea760b79a03129caba14ff))
- autocomplete logging + input layout ([c542e5d](https://github.com/continuedev/continue/commit/c542e5d6153078f42e8eaf4361a4e5bdf24d9bc2))
- Autocomplete not working when lookup is selected ([346f8fe](https://github.com/continuedev/continue/commit/346f8fe191a96eab5903b9a9f5a1e7e5ea1e3ee2))
- avoid parsing md rules as yaml ([6c24132](https://github.com/continuedev/continue/commit/6c24132fab5b1c828aeb2e162c0f07ab6c830d0d))
- binary ([8f24197](https://github.com/continuedev/continue/commit/8f2419740044a5df2ca70905c2b9ecd0fef3389e))
- broken build ([d09711a](https://github.com/continuedev/continue/commit/d09711ab5895aca1c91552e7d9140b3aa0535d77))
- broken help center quickpick ([a83526d](https://github.com/continuedev/continue/commit/a83526d6a49df6589d107695f99c03d02b68629c))
- broken imports ([5fd8d2c](https://github.com/continuedev/continue/commit/5fd8d2cae5926418116031dbab8ce9465bc4c8f9))
- broken imports ([f60009c](https://github.com/continuedev/continue/commit/f60009c2ea7c1c1a980152e5aa3cda17c3f6e64d))
- broken JB build ([0e2587c](https://github.com/continuedev/continue/commit/0e2587cc71f497141ccd5fc807624eac80f8f183))
- broken link ([6a675d8](https://github.com/continuedev/continue/commit/6a675d8edce1e430faa32b5142554bf61b843e0a))
- broken schema config ([0529ae8](https://github.com/continuedev/continue/commit/0529ae8e1e4f61d83f2a4c43c9b9d23a84192ec9))
- browse functionality ([ef5d753](https://github.com/continuedev/continue/commit/ef5d7535f0860978baeb337930f266b1201a7a5c))
- build issues with jetbrains ([267961f](https://github.com/continuedev/continue/commit/267961f5b67f8f4529917fc43ebc1df88db92b51))
- bump extension tester version ([006e1e4](https://github.com/continuedev/continue/commit/006e1e4b3bad2a8545a492cbeeb3238a1bf3345b))
- bump node.js dependencies ([18fd7fb](https://github.com/continuedev/continue/commit/18fd7fbe58a03dfda382f755e261c5f16e88f38e))
- cache ([368d264](https://github.com/continuedev/continue/commit/368d264d221793b7f2439678cef9b8b032135529))
- caching ([1e7aa1a](https://github.com/continuedev/continue/commit/1e7aa1a879c80c0acddf09fbe0d887b7ec9bd31d))
- change from llama3.1-8b to llama3.1:8b ([ea060a3](https://github.com/continuedev/continue/commit/ea060a37567af620cbdb8d665beb3f36cfeada6d))
- chat tests ([7be3bb4](https://github.com/continuedev/continue/commit/7be3bb4161e7a17f7b0ae6e8d3ce365479e70dde))
- ci ([1f47f54](https://github.com/continuedev/continue/commit/1f47f5436dde900b04cf1b307efa64f0fecebd59))
- ci ([f6f2633](https://github.com/continuedev/continue/commit/f6f263375be129e438ab46a3ddbbba0722d3eb24))
- ci ([fa9f86b](https://github.com/continuedev/continue/commit/fa9f86b6fd9ddb8cfab7dc52772c9f5a0623d654))
- CI checks + fix for broken config-yaml types ([2032a8c](https://github.com/continuedev/continue/commit/2032a8c31ce9f30a5996a0fa6566d840a1eae21b))
- ci e2e tests ([ade32d4](https://github.com/continuedev/continue/commit/ade32d428c5ef0fc8f5daefd52571b95387002f3))
- ci tests ([27d20eb](https://github.com/continuedev/continue/commit/27d20eb08b25b142a54641048670b036e201ea1d))
- circular state in redux selector ([5921bfa](https://github.com/continuedev/continue/commit/5921bfac67237dd4ea34dc1aad7856b220adf062))
- cleanup model select ([1933a29](https://github.com/continuedev/continue/commit/1933a29ec4536d4887130703f1b0f94c3cd3db8d))
- Clear all diff blocks before streaming and ensure dispose is run on EDT ([41357cd](https://github.com/continuedev/continue/commit/41357cd00c72b61cd54b80aa3963d047c0bb4184))
- Clipboard is not available in chat window ([ef26b7c](https://github.com/continuedev/continue/commit/ef26b7c80eb0cc503017df8b9abd634f868a9b89))
- close extension after tests ([575a707](https://github.com/continuedev/continue/commit/575a70721b39e41bfe8b860824d5eb606ce29ba9))
- close sidebar when cmd+l pressed on focus ([93c9507](https://github.com/continuedev/continue/commit/93c95075425179d27421c7b54d92c9235d914c3c))
- Cloudflare Workers AI message handling ([7eff255](https://github.com/continuedev/continue/commit/7eff25595c6aa063db37fb2d5394447c8472ba20))
- cmd+shft+l closes sidebar if focused ([#1638](https://github.com/continuedev/continue/issues/1638)) ([92b5c4c](https://github.com/continuedev/continue/commit/92b5c4ccf64a88c461bb32abc9ab02329651e6be)), closes [#1536](https://github.com/continuedev/continue/issues/1536) [#1456](https://github.com/continuedev/continue/issues/1456) [#1564](https://github.com/continuedev/continue/issues/1564) [#1576](https://github.com/continuedev/continue/issues/1576) [#1570](https://github.com/continuedev/continue/issues/1570) [#1582](https://github.com/continuedev/continue/issues/1582) [#1600](https://github.com/continuedev/continue/issues/1600) [#1618](https://github.com/continuedev/continue/issues/1618) [#1626](https://github.com/continuedev/continue/issues/1626) [#1637](https://github.com/continuedev/continue/issues/1637)
- code automatically expands after pressing enter ([42c02be](https://github.com/continuedev/continue/commit/42c02bed5fb61f925eb829cd8e7e39425225cc30))
- codestral template ([d56a33e](https://github.com/continuedev/continue/commit/d56a33ea06f7c9724ffaef86d71bbb8d31840fc8))
- colocation ([fb28272](https://github.com/continuedev/continue/commit/fb282727e92237f6b14e28e3538e04e13b837f1e))
- colocation ([e1d227c](https://github.com/continuedev/continue/commit/e1d227cbc3ea56e607fce5c5ce585c2b901f6029))
- comment out auto apply on MFE ([f61476a](https://github.com/continuedev/continue/commit/f61476a4936f364b4d0adc679da067d5f3ee8411))
- completions ([a2f62da](https://github.com/continuedev/continue/commit/a2f62dad3b4df8a9a243964584eed148835a077b))
- config error handling ([170676a](https://github.com/continuedev/continue/commit/170676a46149a4e507f827aa566f4b1d39344e2e))
- config-types "useSuffix" to "useFileSuffix" ([fa3afd5](https://github.com/continuedev/continue/commit/fa3afd52e766c298a9bfa62c57e8a9b6e695b1d6))
- **continue:** update context for slash commands ([cfac639](https://github.com/continuedev/continue/commit/cfac639e44ff5ba13960bbed6756047a90ed93f2))
- convert `walkDir` to an async generator ([#1783](https://github.com/continuedev/continue/issues/1783)) ([6824497](https://github.com/continuedev/continue/commit/68244977d377e1706a20892abc059bf5cb70bc71))
- copy ([5e122b7](https://github.com/continuedev/continue/commit/5e122b76b3cf5f0f511101bb1b2cea1349fa3a7a))
- **core:** delete indexed docs if re-indexing ([acea620](https://github.com/continuedev/continue/commit/acea620043d2c48826266d4f1f73eaf42819f0c6))
- **core:** remove eslint config and fix errors ([#1457](https://github.com/continuedev/continue/issues/1457)) ([3e1c06b](https://github.com/continuedev/continue/commit/3e1c06b41b7b90a0cad026c0ba0433fb1be6d277))
- **core:** use `TextDecoderStream` for stream response ([#1498](https://github.com/continuedev/continue/issues/1498)) ([09d256a](https://github.com/continuedev/continue/commit/09d256ad562eda0919b9fa2853176319bf4eda36))
- correct formatting ([cbb5c21](https://github.com/continuedev/continue/commit/cbb5c21f591e078df186fd86865830cb8971be03))
- correct llama2TemplateMessages ([#855](https://github.com/continuedev/continue/issues/855)) ([f95a54d](https://github.com/continuedev/continue/commit/f95a54d9c29247680839129c54eae912ba5ca85d))
- correct package.json update ([02968fb](https://github.com/continuedev/continue/commit/02968fb1551c0a26af75340641312e1f9f5a1102))
- correct typo in stop sequence ([0dac76e](https://github.com/continuedev/continue/commit/0dac76edf5f35226cf6b3e99c83a7c02273cc05c))
- corrected typo in the info Alert paragraph ([be55052](https://github.com/continuedev/continue/commit/be55052d230b50635c43727127ee776e67bd4c20))
- cursor focus issue causing unwanted return to text area ([#1086](https://github.com/continuedev/continue/issues/1086)) ([c54cd7a](https://github.com/continuedev/continue/commit/c54cd7a26dd2e4e8743be86c6ac22dd1c8dca774)), closes [#1078](https://github.com/continuedev/continue/issues/1078)
- cursor position ([6284eee](https://github.com/continuedev/continue/commit/6284eeebef984ac1afd3846b606a7745c5dfb49d))
- cycling through chat messages ([b3ac143](https://github.com/continuedev/continue/commit/b3ac14332e8bd89237dfa41847701e3378487145))
- deepseek doesn't support 'https://api.deepseek.com/completions' URL currently. When user selecting code in editor, and then use 'CMD +I' command to let model to modify codes, 400 error happens. ([170b99d](https://github.com/continuedev/continue/commit/170b99d3261b7d2ef32ba9e03a51ac37bb1fb787))
- delete all code_snippets ([b9114bb](https://github.com/continuedev/continue/commit/b9114bbdab90de0dd167c88b57bd96b27ad03524))
- delete button default style ([5423d24](https://github.com/continuedev/continue/commit/5423d24c35c614b0970e472aafa1f1048bf0d970))
- delete old docs index on force re-index ([#1778](https://github.com/continuedev/continue/issues/1778)) ([0d632ea](https://github.com/continuedev/continue/commit/0d632eabbfd512c813e65212c3f5765bbc3fc8c7))
- dependencies ([384fb56](https://github.com/continuedev/continue/commit/384fb562e5aac023301d592a186982a4b575fe98))
- dependencies ([8efb3be](https://github.com/continuedev/continue/commit/8efb3be1b1f2253f9a7fd3035d8f0055de8f426e))
- dependencies ([cc063fd](https://github.com/continuedev/continue/commit/cc063fdd124d66515bdcb9e2c64f575b40cc99d8))
- dependencies ([fccae87](https://github.com/continuedev/continue/commit/fccae8732e1a4f447ff0ab650e9ae717c2022ffc))
- Desktop.browse on linux ([7a11446](https://github.com/continuedev/continue/commit/7a11446bfc5d4cb9c05f64f1377518456d30f530))
- disable completions in the commit box ([2b611df](https://github.com/continuedev/continue/commit/2b611df53ba716c2b550ab3b275c571eed9d2c65))
- disable indexing for unsupported CPUs ([a005aa7](https://github.com/continuedev/continue/commit/a005aa78ac75ac47be3e647e9e23b45eab7dafaf))
- disable lancedb cpu check ([b496777](https://github.com/continuedev/continue/commit/b49677711b4340f57ba671bacda301138c2909fb))
- Do not break completion for unsupported FS providers ([4d1a33f](https://github.com/continuedev/continue/commit/4d1a33fd794a7415b0cc3b8de1d349fbba5a01cb))
- do not filter context providers if not in editMode ([b2182b9](https://github.com/continuedev/continue/commit/b2182b9c8568a38d3af179909fb305e1459f5276))
- do not write to the cache if caching is disabled ([b29ed9d](https://github.com/continuedev/continue/commit/b29ed9d34aa7ae343a2fd0c767f315e46e9aa110))
- docs and install dependencies task for new directory structure ([#551](https://github.com/continuedev/continue/issues/551)) ([6ef8ae6](https://github.com/continuedev/continue/commit/6ef8ae6d5ab7efb1e928ff6474b6ac7a804a34cc))
- **docs:** contributing guide ([5a06dfe](https://github.com/continuedev/continue/commit/5a06dfe1fb865bed0ca76ff23816da6396a0b27d))
- **docs:** correct openai api key name ([9cc25c1](https://github.com/continuedev/continue/commit/9cc25c18c9fff5fc6ac4d56153667b0e8311920c))
- **DocsCrawler:** Add response.ok check and improve link splitting ([d613b5a](https://github.com/continuedev/continue/commit/d613b5a7da5ad15c8b4b7dfe949812045f69635c))
- don't override embeddings model name with UNSPECIFIED ([bc79388](https://github.com/continuedev/continue/commit/bc79388634ebcfcf2e38aa240441f9939e9de85a))
- don't return empty string ([403eb38](https://github.com/continuedev/continue/commit/403eb38b9e3e38c277daed4ac6a2b6ac54353570))
- dont toggle models when toggling assistants ([5bd8dd4](https://github.com/continuedev/continue/commit/5bd8dd4cb759bbabc35bdc00c66d88e4a175f7a8))
- double ssh retries ([c9e2ebc](https://github.com/continuedev/continue/commit/c9e2ebc4589f7a06e804daee7f2ce6d7b2b66ce2))
- dropdown with the height of the sticky div ([#605](https://github.com/continuedev/continue/issues/605)) ([a8d6ff0](https://github.com/continuedev/continue/commit/a8d6ff04e43a81a3c76373469ffebdfcfa8ac9f2))
- editor focus handling on click events ([#716](https://github.com/continuedev/continue/issues/716)) ([f1660a5](https://github.com/continuedev/continue/commit/f1660a533146c420253df6e2dec0e0d4a2a9ebf4))
- Eliminate Uncaught TypeError in dev tools ([d342825](https://github.com/continuedev/continue/commit/d3428256543bcc5ab269df93c949b56df92793f2))
- Empty file inserts incorrect content ([ba9027b](https://github.com/continuedev/continue/commit/ba9027baea44ca51d299804636771d1a084d5fa7))
- empty last message ([d3f7795](https://github.com/continuedev/continue/commit/d3f7795e6133fe7452c0626e6a54fb229bacbd7b))
- enable OSR for mac after validations ([db77b50](https://github.com/continuedev/continue/commit/db77b5020d9b93cb4a7504e213d81940dbee9eaa))
- end thinking for reasoning models when request is complete ([5109685](https://github.com/continuedev/continue/commit/51096858ab1ddc2bee534c1ea848be2b4bdcfb3f))
- ensure code preview uses one more backtick than in the selected code as a fence ([#742](https://github.com/continuedev/continue/issues/742)) ([9ce7770](https://github.com/continuedev/continue/commit/9ce7770066cf870d2d1f36883f5e867157373eda))
- ensure template variables are only processed for stdio MCP servers ([99b1ab2](https://github.com/continuedev/continue/commit/99b1ab2ebe51b4678722703922ecca023afa911c))
- Ensure that the state is cleared after refresh, and files->uris when the file changes ([abd8907](https://github.com/continuedev/continue/commit/abd890738111e51bf12a700f9e5e8e66a7c76235))
- Ensure valid line index for highlighter ([f86c95e](https://github.com/continuedev/continue/commit/f86c95ebdbd1851bcbf0e6f22d18a348a8a154ed))
- error ([4c7f918](https://github.com/continuedev/continue/commit/4c7f9182665a259fc6b9cf10c93e696e97400a2c))
- error printing bug leading to uncaught err ([3af4d6b](https://github.com/continuedev/continue/commit/3af4d6b1edeac113499cc9626cea75a5083b1030))
- exclude open-codestral-mamba from supported models ([c6591f8](https://github.com/continuedev/continue/commit/c6591f836ea73e1244a0e965111f7c599669d9fa))
- **extensions:** schema for db ctx provider ([#1534](https://github.com/continuedev/continue/issues/1534)) ([6fdad55](https://github.com/continuedev/continue/commit/6fdad553b6ac7f64c1bb23617dbe313e16174a56))
- failing azure llm test ([546b52a](https://github.com/continuedev/continue/commit/546b52a2df928687d56924f593108f5bb9235712))
- failing test ([ec2a72d](https://github.com/continuedev/continue/commit/ec2a72dfabbe7bd9c220b18b55145f431e19f3bf))
- failing test ([002ecc9](https://github.com/continuedev/continue/commit/002ecc9b519472e0968e9f227894f3143c87f1ec))
- failing test ([07b6b0c](https://github.com/continuedev/continue/commit/07b6b0cbefe86f2efa5488a33060ee9eb9f20e4d))
- failing tests ([88ccb74](https://github.com/continuedev/continue/commit/88ccb74f7e69cf816d27f15a318a42c11f6d2aa5))
- failing tests ([cb2e0a7](https://github.com/continuedev/continue/commit/cb2e0a7c0e9330d9b79a3e9878c851b9eba92ade))
- failing tests ([7b52ff7](https://github.com/continuedev/continue/commit/7b52ff74d4009cb85c32a5e9482bd3624931d21e))
- failing tests ([8d29d41](https://github.com/continuedev/continue/commit/8d29d4158d0fe21b0c0c4722e8d23715acf9ebdf))
- failing tests ([2b828b0](https://github.com/continuedev/continue/commit/2b828b0e7b455bf6b0f9ae5a6f6a705ab4cefe6a))
- field name fix ([0de1a2f](https://github.com/continuedev/continue/commit/0de1a2ff8937452815311c806a038a7f6d75fdf8))
- filepath replacements in prompt files ([#1939](https://github.com/continuedev/continue/issues/1939)) ([c0923a0](https://github.com/continuedev/continue/commit/c0923a0dd7ca3a6e9dcbd9b02bfc34250269d2ef))
- fix [#709](https://github.com/continuedev/continue/issues/709) ([#712](https://github.com/continuedev/continue/issues/712)) ([9ae07cd](https://github.com/continuedev/continue/commit/9ae07cd711d011faaf9a79952c0e213a6aa72e87))
- fix 100% indexing progress ([7f214ad](https://github.com/continuedev/continue/commit/7f214adcbdca33ebb7270f721fd57eeeb0bac1e8))
- fixed misallignment in tests caused by linter ([15e93dd](https://github.com/continuedev/continue/commit/15e93dd5d0dc3b5dcd00504833beacbddbfe44f3))
- format code ([1d000be](https://github.com/continuedev/continue/commit/1d000bec682d1d723bf6748f34c3dd86b684d775))
- formatting ([9efd0e1](https://github.com/continuedev/continue/commit/9efd0e128ef464d45379e84abf15e98ad1b24d40))
- formatting ([2284291](https://github.com/continuedev/continue/commit/22842919489ad272edfa182e9f8af3a26d9813ae))
- formatting ([4d70d3d](https://github.com/continuedev/continue/commit/4d70d3dcbd646b1aa1cb6e586439459dec2db462))
- free trial config.json updates ([905f064](https://github.com/continuedev/continue/commit/905f064543e71864eaeb4e039e5d55fe2da845fd))
- fullscreen gui retains context when hidden, fixed fullscreen focusing ([#1582](https://github.com/continuedev/continue/issues/1582)) ([679e26d](https://github.com/continuedev/continue/commit/679e26d4a8a81402d57a64d819f347d9f56d42e0))
- gemini tool calls with MCP ([9ca4ad4](https://github.com/continuedev/continue/commit/9ca4ad4afe04fbfc3a6e0e6d447dd81ff22b4369))
- getting diff ([9431131](https://github.com/continuedev/continue/commit/9431131f1cbbc5fe742c033a8baf970241c63378))
- **google:** remove unnecessary parameter ([#394](https://github.com/continuedev/continue/issues/394)) ([938c1db](https://github.com/continuedev/continue/commit/938c1db5b37d5332ff5d188f4fa79f3bc6b7549a))
- grab PATH for mcp connections ([60ec3ac](https://github.com/continuedev/continue/commit/60ec3ac452f0b25d176728dfe6e83e4f34ade745))
- grammar ([58feb0b](https://github.com/continuedev/continue/commit/58feb0b8622e5f64737594ccbfd94aff2e9687c5))
- **gui:** ctx rendering w/ renderInlineAs: "" ([#1541](https://github.com/continuedev/continue/issues/1541)) ([8a92f43](https://github.com/continuedev/continue/commit/8a92f4312693549a1590e99c70152b843d59b119))
- **gui:** passing style props on StyledTooltip ([41e1eb5](https://github.com/continuedev/continue/commit/41e1eb582bec8676fc973c3a2b76259344fbb317))
- **gui:** typo ([3d53809](https://github.com/continuedev/continue/commit/3d5380908292bb4373a122a802f2011dd0283f62))
- handle apiType for azure w/ proxy ([036e196](https://github.com/continuedev/continue/commit/036e19618b86600e4e9c5ad6e6ee5a98e55e15f1))
- handle closed webview on quick edit ([#1942](https://github.com/continuedev/continue/issues/1942)) ([fe05db5](https://github.com/continuedev/continue/commit/fe05db541dafc45c9870d52d9f15e812e1ea90ad))
- handle deleted blocks ([c1157db](https://github.com/continuedev/continue/commit/c1157dbf02f6750d385b28006c90202e6b159d5a))
- Handle empty addedLines in revertDiff ([f280319](https://github.com/continuedev/continue/commit/f28031902f41c4ae9f7cab01398216272950b7d0))
- handle line removal diff ([#1744](https://github.com/continuedev/continue/issues/1744)) ([6126eca](https://github.com/continuedev/continue/commit/6126eca35ab981c7b8cd6a56769dfe7cb9e69349))
- handle optional params in EditSlashCommand ([#745](https://github.com/continuedev/continue/issues/745)) ([0141229](https://github.com/continuedev/continue/commit/014122957ff6e087b03def436b95797e59c6f6cb))
- Handle status update in DocsService ([ac3df81](https://github.com/continuedev/continue/commit/ac3df814e45343b07f63c99e8710f4a6b185074e))
- handle when dir might be a file path ([e43d3bd](https://github.com/continuedev/continue/commit/e43d3bda6aa833bfe686b8c84dfa64d43fa08748))
- hardcode `fontSize` for tool call status msg ([f78969b](https://github.com/continuedev/continue/commit/f78969b147a8aecb5c9e5162d080cdafb3f4adb7))
- hmr issues with non-react component exports ([0e55c08](https://github.com/continuedev/continue/commit/0e55c08be3ef5dcaec5305f41ae871ae6903b6c7))
- hotkey for new session ([#572](https://github.com/continuedev/continue/issues/572)) ([1d3acd9](https://github.com/continuedev/continue/commit/1d3acd9f194802d78c99cd407bcb72543aa0ff19))
- **HttpContextProvider:** support all possible local servers via a library ([c3b2552](https://github.com/continuedev/continue/commit/c3b2552ed5e47eef8dc2a691d5f76f30b61ceafd))
- if rules ([a6dd5a8](https://github.com/continuedev/continue/commit/a6dd5a83dedcae099f320a2da9362490eadde299))
- ignore CSV files ([#1972](https://github.com/continuedev/continue/issues/1972)) ([1272f34](https://github.com/continuedev/continue/commit/1272f3402a1fc12e5eb0566ad4a4aadc3db7dc06))
- Implement RepoMapContextProvider ([48c442d](https://github.com/continuedev/continue/commit/48c442d1c687b15d5612786e87a09b016714abd6))
- import posthog as type, use inline import ([57a84dc](https://github.com/continuedev/continue/commit/57a84dcfe16e3cdebea54f7a0b0fe354f0d8ad2b))
- Improve error handling and ensure coroutines continue correctly in case of failure ([46bd573](https://github.com/continuedev/continue/commit/46bd573a4296bb8ce8dc4c43b7293ff575655e53))
- improve textarea ux ([#1901](https://github.com/continuedev/continue/issues/1901)) ([228bc30](https://github.com/continuedev/continue/commit/228bc30e895b87e06c170775974c49e2781d9ae0))
- include access to the Documents folder ([03839c3](https://github.com/continuedev/continue/commit/03839c30813f3242d9a1fce91f39af110d8d85a7))
- incorporate feedback ([98efbbd](https://github.com/continuedev/continue/commit/98efbbd80f075928a45334f8cc4103221c2656c9))
- incorrect scroll logic on inline edit ([ed3af3f](https://github.com/continuedev/continue/commit/ed3af3f3fc49f6678d8a1f940d06aa62bec1f4b5))
- intellij freezing ([1f602e5](https://github.com/continuedev/continue/commit/1f602e53c29eb28d98754252823ad8f40f839d1b))
- isRuleActive ([2b97efe](https://github.com/continuedev/continue/commit/2b97efe5c6d045b461c76068c0e06df298d95902))
- issue 3732 ([ce5d664](https://github.com/continuedev/continue/commit/ce5d664f828de8681ee7129dfec3c16cad667c46))
- **JB+GUI:** arrow keys in GUI on linux ([9110904](https://github.com/continuedev/continue/commit/9110904e7d4f727339666ce235388397b8c4ff99))
- **jb:** meta keybinding issues ([b4dad78](https://github.com/continuedev/continue/commit/b4dad78a727feed0ce18769a8d3f9cfd00a3bd57))
- **jb:** remove markup from `edit` model title ([f6d334a](https://github.com/continuedev/continue/commit/f6d334a190bf1132c33a68f472aaeb2e5389399f))
- **jb:** set `setOffScreenRendering` conditionally ([fd66ff7](https://github.com/continuedev/continue/commit/fd66ff7d464d3138a127e2035bca88f6d42c4402))
- **Jira Context:** add api abstraction ([cc9b960](https://github.com/continuedev/continue/commit/cc9b96038bb0b88156729d1c39e6eec1794fe76a))
- jira ctx provider ([9d24d34](https://github.com/continuedev/continue/commit/9d24d3402f3e9f9937bfdff70fa5c4fa120b2dbf))
- keyboard shortcuts test on linux ([e45aa16](https://github.com/continuedev/continue/commit/e45aa164c64a52936c7dd1c0467548aadf821639))
- layout alignment ([#1779](https://github.com/continuedev/continue/issues/1779)) ([6e8fc12](https://github.com/continuedev/continue/commit/6e8fc1247c44c344a48db9758437cb7b3028591c))
- lint ([f575721](https://github.com/continuedev/continue/commit/f575721980a596cdb08be8a2e472ca26e6012325))
- lint ([3ba1153](https://github.com/continuedev/continue/commit/3ba1153243753682ca19b26031c2c3c73394b5f2))
- lint ([03cd1a3](https://github.com/continuedev/continue/commit/03cd1a338f03fc1baa87feb02bd6e56111fc0b13))
- lint ([747d380](https://github.com/continuedev/continue/commit/747d3806a99abf7f55823c2fbc06c74448ba3c1c))
- linting error ([67ab058](https://github.com/continuedev/continue/commit/67ab05821191d75a201e516b725a679671f94734))
- linux key ([9618327](https://github.com/continuedev/continue/commit/9618327cf45687f18aacdc04d62da86ce2d1e520))
- linux test env ([9f2f9d8](https://github.com/continuedev/continue/commit/9f2f9d895b2fe6678f49e394e4657f044e2aef2a))
- listbox z-index issue ([1d2bfcd](https://github.com/continuedev/continue/commit/1d2bfcd4b2ad7c3afef97348c55a1c668610ea07))
- listDir ([a41a4bd](https://github.com/continuedev/continue/commit/a41a4bd2df5807bd216a4b23eabe8656ca098cdb))
- **llm/Ollama:** changed buffer handling like in streamChat() ([f1f8967](https://github.com/continuedev/continue/commit/f1f8967b6c4b422428fcad0575c0b24ae78bf34a))
- load last session when completing edits ([1e33400](https://github.com/continuedev/continue/commit/1e334008c08eb8796510ba6aaac434073c44b3b8))
- local build script ([#810](https://github.com/continuedev/continue/issues/810)) ([76fe78a](https://github.com/continuedev/continue/commit/76fe78a1a3c05b0d6dd5ed6d15575e20e797cfa6))
- log llm completition in edit command ([3094180](https://github.com/continuedev/continue/commit/30941800b04c59b9a45375d6934a4fb79cac0141))
- logs ([e3cf75e](https://github.com/continuedev/continue/commit/e3cf75e0779d3bc02a468ea6551e2499979c08f7))
- mac build issues ([9946a5c](https://github.com/continuedev/continue/commit/9946a5c05f85d20e2d766719a861eb0f8a55019c))
- major fixes to getTheme ([c6002ba](https://github.com/continuedev/continue/commit/c6002ba2ac905bd5439173e6430676bdc3594a87))
- make `env.apiVersion` required for azure openai ([4355618](https://github.com/continuedev/continue/commit/4355618079808bdfc3240022e79a0a9dd8457b02))
- matching context code ([64fb928](https://github.com/continuedev/continue/commit/64fb928b8ff9c0f7753a8b8d374abc179de1f4d5))
- mcp context provider bug with issue 2467 ([98f07be](https://github.com/continuedev/continue/commit/98f07bef818cf5acc4de2828cbeaa0b4b499fc53))
- MDX compilation issue ([aca82d2](https://github.com/continuedev/continue/commit/aca82d2ef36a5bd57119343790875e2acf7819b3))
- merge bugs ([009e578](https://github.com/continuedev/continue/commit/009e578416982a90af05539420a7137ccd2d03c1))
- messages ([0b07020](https://github.com/continuedev/continue/commit/0b07020d7dd9bf59ca1d804668a215174273625b))
- minor issues ([#581](https://github.com/continuedev/continue/issues/581)) ([570f61d](https://github.com/continuedev/continue/commit/570f61de5a404559b4ff69eb5cd2fa216e70e872))
- missing arguments ([452b242](https://github.com/continuedev/continue/commit/452b2422d18c05dd28dbfa6fd127f4c3e0b0223c))
- missing cd ([0d88b8f](https://github.com/continuedev/continue/commit/0d88b8f379d161962a72626fe2af583cce0f3fe0))
- missing Content-Type header in OllamaEmbeddingsProvider ([#1855](https://github.com/continuedev/continue/issues/1855)) ([841294d](https://github.com/continuedev/continue/commit/841294d15f3ad6ea4a9f7cf277fbcb905d9bd978))
- missing context items ([bdc2ec8](https://github.com/continuedev/continue/commit/bdc2ec8374ac9e7d14fc440ea80fa438005735e8))
- missing env ([1ebd3a3](https://github.com/continuedev/continue/commit/1ebd3a31bab5ed31374518022c5f1b7658c379f2))
- missing package ([e284cec](https://github.com/continuedev/continue/commit/e284cecd6584b14bee43794d0d238d64c734a10a))
- missing useLegacyCompletionsEndpoint in yaml schema ([a4b4395](https://github.com/continuedev/continue/commit/a4b4395649e9fa7888f8204ddcf56b88632b5b90))
- model name ([b1ec201](https://github.com/continuedev/continue/commit/b1ec2012c649ff29e84d6682234328e9c2fdb792))
- models and mode are not synced ([57441da](https://github.com/continuedev/continue/commit/57441dad6696eeb6d4bd6d769aec06e389367a53))
- modify access modifier public to private ([c36f233](https://github.com/continuedev/continue/commit/c36f23322265a0d9c28eb401acf49d76294a1249))
- move autocomplete logic off of EDT ([c46083d](https://github.com/continuedev/continue/commit/c46083d4115a79938bc6ae2b9421f36803b0a992))
- naming ([ba39842](https://github.com/continuedev/continue/commit/ba3984290ddb875608845904d31c5a5d57b1bf03))
- naming ([42a03ad](https://github.com/continuedev/continue/commit/42a03ad020634a7c6be719a70bf40144582682dc))
- naming ([fb1a75d](https://github.com/continuedev/continue/commit/fb1a75dcf33fa38863bda4f5b88c1a567aaf0269))
- navigation bug ([fab5347](https://github.com/continuedev/continue/commit/fab53472a931b49d7f0c19af22263df6b54bc292))
- not sure how that button fragment got there ([880f1de](https://github.com/continuedev/continue/commit/880f1de80d2bc33635cc9ebed01ae84e2c17c640))
- number of completions to generate is not supported ([db90b09](https://github.com/continuedev/continue/commit/db90b0954b36e856258eed2b61af8bf107f13510))
- old docs ([b52910b](https://github.com/continuedev/continue/commit/b52910b43351e5df8c07cfb27beca8fba5463074))
- ollama tool use ([82ac684](https://github.com/continuedev/continue/commit/82ac6848cabca21122995460328b8f4420ae3222))
- onboarding telemetry ([445dba3](https://github.com/continuedev/continue/commit/445dba3bb60654ac9c3f8d245f896d490574c3a1))
- only alert free trial if using free trial ([f0e73ef](https://github.com/continuedev/continue/commit/f0e73ef63a8fd8bb7ce313f9ed5439c7c68969bf))
- only perform lancedb cpu check for linux ([e0ee7d1](https://github.com/continuedev/continue/commit/e0ee7d1e446b18c05ed3166b0aa3b199ec021f1a))
- open new session from any page ([258bbc6](https://github.com/continuedev/continue/commit/258bbc6b5cbbd3b897e8045df247bb2a1fc21f94))
- overly eager suffix matching ([556a8c5](https://github.com/continuedev/continue/commit/556a8c5ee3f72311a384c64ba14fde7122e68741))
- path ([d01f243](https://github.com/continuedev/continue/commit/d01f24309b490799014dca0fd03775c39745d660))
- path ([3ecc89e](https://github.com/continuedev/continue/commit/3ecc89eaf55b970e1a494f88fed0850c4b85c565))
- pathing issue in jb ([c33e390](https://github.com/continuedev/continue/commit/c33e390427e459a5426ee7669733c2b407649e40))
- pause between tests to avoid db lock ([4cb84c1](https://github.com/continuedev/continue/commit/4cb84c11b09dac6eb0791e35b95ff6f571130391))
- placeholder ([8ded13a](https://github.com/continuedev/continue/commit/8ded13a8f8e54df4951b547d468782117fb11b8b))
- policy precedence ([de6a0ae](https://github.com/continuedev/continue/commit/de6a0ae36c43753e768bf0164dfbe1b938da790d))
- preserve indentation in code blocks ([#1554](https://github.com/continuedev/continue/issues/1554)) ([574a392](https://github.com/continuedev/continue/commit/574a3923a6f97d8995561da3654aae3aca9ae439))
- preserve system messages as user messages ([d60a1c1](https://github.com/continuedev/continue/commit/d60a1c1b1f3174cd501eca69746009bdeb4c6f7f))
- prettier ([4d278e4](https://github.com/continuedev/continue/commit/4d278e479ae02f0d02ccff8db2448d6b38b3d3e5))
- prevent autoscrolling on new tool msgs ([678a75f](https://github.com/continuedev/continue/commit/678a75f8f7b97b0f75407c6e1b1dd78c415d22eb))
- Prevent duplicate context provider addition ([2d6eacb](https://github.com/continuedev/continue/commit/2d6eacbeab8eb89ddc32311ade4efe3ccb272bca))
- Prevent multiple resumes in continuation ([1e96bfb](https://github.com/continuedev/continue/commit/1e96bfb9683fa0a45437f16b419be066917351cb))
- Promise arrays being returned. Added a new definitions-only context provider. fix: typescript tagging. ([#835](https://github.com/continuedev/continue/issues/835)) ([00eeb00](https://github.com/continuedev/continue/commit/00eeb0024178953172941bba293808be4ca5eed2))
- prompt format ([0f6d280](https://github.com/continuedev/continue/commit/0f6d280234c459e1935b62e5244f4577d64fac53))
- promptblock parsing ([eb6735e](https://github.com/continuedev/continue/commit/eb6735ee25da93e01a7b09223960b8975b41d898))
- Properly ignore ignored folders when generating /onboard context ([f7bca6a](https://github.com/continuedev/continue/commit/f7bca6a61d56b6e17b100294e605d0e2bbe0239b))
- protocols ([c8a24c0](https://github.com/continuedev/continue/commit/c8a24c0c9040a0da02bdd215b77b0320c160256f))
- qwen completions missing whitespaces ([75dce9c](https://github.com/continuedev/continue/commit/75dce9cbd14cc374d1ce4921522f3423202be7b1))
- **reg:** :ambulance: remove log except when in NODE_ENV === "test" ([f60fd1c](https://github.com/continuedev/continue/commit/f60fd1c6188567c67d3d8285fd6c2c5d6acc5bfa))
- regex ([05a3eec](https://github.com/continuedev/continue/commit/05a3eec21ed1c26aa4d9825e6be5e209cdb9563c))
- reject logic ([5944fca](https://github.com/continuedev/continue/commit/5944fca7374851869e469ed1cd3f13056de5c8b3))
- remove `any` from promptTemplate passing ([7834d26](https://github.com/continuedev/continue/commit/7834d266330625915542913f2d44ac0d80875d7e))
- remove debugger statement ([3e4c75b](https://github.com/continuedev/continue/commit/3e4c75bf461c36c2200ac2922670bb76a612b292))
- remove deugger ([3e8e29e](https://github.com/continuedev/continue/commit/3e8e29e8f2ce18e138df64441e436d3a07ffa45e))
- remove duplicated context comments ([0c217a4](https://github.com/continuedev/continue/commit/0c217a40082c2c244de4f4f8c955d96314f130da))
- remove gifs from media ([77d321f](https://github.com/continuedev/continue/commit/77d321ff82dcd34b55d0a0253eb45672be774fe1))
- remove invalid schema ([2355eb6](https://github.com/continuedev/continue/commit/2355eb62a5c3c5034b528ab17e8ad7a0b661bc7c))
- remove mismatch between last selected profile and current profile ([d9eb118](https://github.com/continuedev/continue/commit/d9eb118c514fc2a4364f532eb6438588fc9f712c))
- remove some backup files generated by pkg if present ([#1287](https://github.com/continuedev/continue/issues/1287)) ([9f160ad](https://github.com/continuedev/continue/commit/9f160ad11a1d36bfc92d2da2a8ea31721652b3e4))
- remove stale code ([cd97833](https://github.com/continuedev/continue/commit/cd97833be88ecaf3c2860dc41ca57c9e2b18ad48))
- remove testing logic ([5df8177](https://github.com/continuedev/continue/commit/5df81770df526c69dd71f2421a9052deb17accf6))
- remove tooltips when switching files ([200d291](https://github.com/continuedev/continue/commit/200d29103ea97db06283f2d4b5e0eb9b6afb86df))
- remove unnecessary baseUrl ([5f159fb](https://github.com/continuedev/continue/commit/5f159fbd81c4b8680cbad8b3875aa54e2079928e))
- remove unused argument ([7dd2c22](https://github.com/continuedev/continue/commit/7dd2c22632d50726271655d5c5d027a8219e6862))
- remove unused imports ([1b82d39](https://github.com/continuedev/continue/commit/1b82d39ac5b8e0cfb990a4968b3e8bc81af768ce))
- rename edit folder ([ab1ad0a](https://github.com/continuedev/continue/commit/ab1ad0ae48eeceee87043cae9a7862a1c4f70da0))
- replace null with empty string in getSidebarContent function ([#822](https://github.com/continuedev/continue/issues/822)) ([50d1188](https://github.com/continuedev/continue/commit/50d1188479be206d1a4ed5dcd258d4101ae4f8ba))
- report indexing errors in webview ([6a67514](https://github.com/continuedev/continue/commit/6a67514fcf108bd0eb6989777abaed02ce2e61e7))
- reset config errors ([cc199c2](https://github.com/continuedev/continue/commit/cc199c28634d006d38416533b06f26e001cea3fe))
- Resolve proxy error when adding Azure OpenAI model ([17dbfe6](https://github.com/continuedev/continue/commit/17dbfe6bfd38f67b7cb535da00fa626cdaf4f690))
- Resolve type error in env comparison ([45eacf4](https://github.com/continuedev/continue/commit/45eacf4ebbd99a97eef32eef01d780a1f8c57b7a))
- Resolved errors related to incorrect URIs ([0a7da20](https://github.com/continuedev/continue/commit/0a7da2067e8d8567084d05f69038b7ca95464d1f))
- restore all e2e tests ([d3a6eeb](https://github.com/continuedev/continue/commit/d3a6eebeb7b5d96133d1c13e533913dc5ec2f19c))
- restore cache ([91ea00d](https://github.com/continuedev/continue/commit/91ea00d284227f3875daf17591b15409004372ac))
- restore comments ([067a3a8](https://github.com/continuedev/continue/commit/067a3a843baf5a0e12bd5b6f21f37a504836286c))
- restore tests ([553c751](https://github.com/continuedev/continue/commit/553c751562afb736fb914549ba3d2c75e5a4b305))
- return ctx items from prompt file ([84df7f4](https://github.com/continuedev/continue/commit/84df7f45bf91c70da79901c0a44cc3b156655f6f))
- revert file changes ([7a44a38](https://github.com/continuedev/continue/commit/7a44a38c860cfbd0eeab35d8fdb3cb198edad680))
- Revert filepath changes ([3df34b7](https://github.com/continuedev/continue/commit/3df34b725e873fcd42058474d83ef634e21d5cad))
- revert launch.json change ([b3abfb8](https://github.com/continuedev/continue/commit/b3abfb80d71c16a0e51cc980d4262dbd218d6463))
- reverted the url of llama.cpp back to 'completion'. ([e8523a2](https://github.com/continuedev/continue/commit/e8523a2b07c7a5d1539138ee8e4a4836fea7a36b))
- rules ([bcae5b3](https://github.com/continuedev/continue/commit/bcae5b3cf210f1a8ced3b2c913961ed16aaf2ea9))
- rules ([9e57412](https://github.com/continuedev/continue/commit/9e57412fe161a9451aaaa651a9f9ed2d7b6b5517))
- rules ([f29110f](https://github.com/continuedev/continue/commit/f29110f98b1c71f4832d6b7ff0041e342e2a0c4a))
- rules ([b39cb83](https://github.com/continuedev/continue/commit/b39cb836ad6180226d39abe85f5637a6e2dac44d))
- sanitize lance table names ([5d3b2cb](https://github.com/continuedev/continue/commit/5d3b2cb5070505578cc16e67d2309a40a403bc94))
- scaffolding for future language syntax highlighting ([647656c](https://github.com/continuedev/continue/commit/647656cd3d007c016bea372bdfcb5d69f9d1d52a))
- scroll issues w/ code blocks ([#1688](https://github.com/continuedev/continue/issues/1688)) ([ceb8da0](https://github.com/continuedev/continue/commit/ceb8da0b20db70707a3443ff6f705bba6922488d))
- scroll to the bottom on history change ([b94ccc6](https://github.com/continuedev/continue/commit/b94ccc6db557f705ba4f810ed56c55ce21f383dd))
- selected profile id writing to global context in org profile rectification ([47755d1](https://github.com/continuedev/continue/commit/47755d1cba3b8c7cb5c0b9a19bf8ca0a92eec850))
- selector for `isSingleRangeEdit` ([badeb1c](https://github.com/continuedev/continue/commit/badeb1c88b7e1f8eb711a8a2cfc8f341ac22f67e))
- send feature flags ([2e3fdb3](https://github.com/continuedev/continue/commit/2e3fdb34e2e2358232cc12fcf0a0c2d16a3ef0cc))
- serve to localhost ([7eccf2d](https://github.com/continuedev/continue/commit/7eccf2ddb9308111686251474fb79fa03dfc87d6))
- set DEFAULT_CONTEXT_LENGTH to 8192 ([f163768](https://github.com/continuedev/continue/commit/f1637685c651180d1fc969c2f11712fda69eb9ee))
- settings persistance ([56f3803](https://github.com/continuedev/continue/commit/56f38038b35cedc2aa1bca1b09780b21c811bd83))
- setup ([6a93eae](https://github.com/continuedev/continue/commit/6a93eaeb0faf685dff216ecca850f9b2ef3cf2c3))
- show error is GH sign in failed in vscode ([e92e052](https://github.com/continuedev/continue/commit/e92e052b16fb91173aa4dc85d4e80fa569b8e60a))
- sign in ([fa5d2c5](https://github.com/continuedev/continue/commit/fa5d2c5aa2cdcc580486f81fde3ae69e9d3069e5))
- sign in btn + colors ([1b669bf](https://github.com/continuedev/continue/commit/1b669bf80e2d81e4aa19c4d3f2dca730ddd6d4f8))
- Skip duplicate checks at document end or blank text ([decd04e](https://github.com/continuedev/continue/commit/decd04eddd4965b9f76c7a9a3abd276d8d2e1692))
- snippets in fim prefix ([51e1c42](https://github.com/continuedev/continue/commit/51e1c4216baa38d73141aca1d1e681eabf044b4b))
- some typos ([#478](https://github.com/continuedev/continue/issues/478)) ([775d051](https://github.com/continuedev/continue/commit/775d051b4bcdbe07fbd38fae4d3e36e79234eb56))
- spread tool degs ([4ddd275](https://github.com/continuedev/continue/commit/4ddd2755996012677c15069648158bd4334fa3dd))
- sqlite binary build on fresh clone ([#1433](https://github.com/continuedev/continue/issues/1433)) ([d124aa0](https://github.com/continuedev/continue/commit/d124aa0534303b58b55bcc2de4ec460b4f499178))
- stream ([c497517](https://github.com/continuedev/continue/commit/c49751713248e27f060b6eee11179e13b14c5adf))
- system message ([c2707c6](https://github.com/continuedev/continue/commit/c2707c6988a9b6368f183f0602de0cc2872726da))
- test ([7266efb](https://github.com/continuedev/continue/commit/7266efb4e8490accd69ad5818bd9c35fe6f59e5c))
- test env path ([71a151f](https://github.com/continuedev/continue/commit/71a151f399d514497ee917eca521863d07b2a270))
- test timing ([878e211](https://github.com/continuedev/continue/commit/878e2116d6a97c31cad97432d785aaf3b6deb196))
- tests ([35c674c](https://github.com/continuedev/continue/commit/35c674c724c21c6dc0b699a37f868e68c62f1259))
- tests ([cafb2e9](https://github.com/continuedev/continue/commit/cafb2e93c7a54b4a35ecc8a209f53982b3cc26d4))
- tests ([b786591](https://github.com/continuedev/continue/commit/b786591415d79119845e01376bf5dc97c0676b23))
- tests ([ff543e8](https://github.com/continuedev/continue/commit/ff543e8b321e4e48a088ba296ebac4504b97356b))
- tests ([81acb44](https://github.com/continuedev/continue/commit/81acb446019a773ce2df98467e4dcb8e5c2a428e))
- tests ([deea8f1](https://github.com/continuedev/continue/commit/deea8f1f44403b6d7d7bb4236ff5acb8d38bca01))
- The selected item in inline chat is always the first one ([97bc074](https://github.com/continuedev/continue/commit/97bc07453ba067f0438192d8dc6cc1cc9a86d962))
- timeout ([b9f161f](https://github.com/continuedev/continue/commit/b9f161f6d8b4e7400dff25912881f41f472a9137))
- timeouts ([0bf9169](https://github.com/continuedev/continue/commit/0bf9169a968a42dfc083696b3e8be5f1997ad7e5))
- timeouts ([0c4d9eb](https://github.com/continuedev/continue/commit/0c4d9eb819b9f1a41cddd7d0b84dc0e21ee372e9))
- timeouts ([4c90f3b](https://github.com/continuedev/continue/commit/4c90f3bdd32761b7f34a6f05334dffc085805964))
- tip tap undo and redo ([88e34fb](https://github.com/continuedev/continue/commit/88e34fb69caae3750621f9f93253a7425535928f))
- tool use call ([c73f72f](https://github.com/continuedev/continue/commit/c73f72fbb379dae08aa41ac07608d9dc2aa8bc83))
- transpile dynamic imports to cjs ([#1975](https://github.com/continuedev/continue/issues/1975)) ([1e3e8eb](https://github.com/continuedev/continue/commit/1e3e8ebe408de1bbb5f9fe0dc61fefef3b85ca69))
- truncate `tagToString` to max filename len ([afb6bd0](https://github.com/continuedev/continue/commit/afb6bd0fb53505e29e961f5a78f0afe36c3b3071))
- ts ignore declaration file issue w/ dbinfoz ([#1945](https://github.com/continuedev/continue/issues/1945)) ([477ecd6](https://github.com/continuedev/continue/commit/477ecd63acc85be25ec992d4329f6460c42c6785))
- tsconfig ([8549981](https://github.com/continuedev/continue/commit/8549981e200e7e62f4a5895babc1c87c45b59db0))
- type errors ([f976e54](https://github.com/continuedev/continue/commit/f976e54dc3b2b8072aef3ecadf259ee1e5299956))
- type errors ([f575a36](https://github.com/continuedev/continue/commit/f575a3683c499f04766ac68af6b8e214ed2c3514))
- type errors ([cd82f4c](https://github.com/continuedev/continue/commit/cd82f4c61fdff90c8ed0e845a3b2ee8b0ce1be9d))
- type errors ([ea6b234](https://github.com/continuedev/continue/commit/ea6b2344d0b2cde7def58de549e604142a40a353))
- type errors ([f19b6b8](https://github.com/continuedev/continue/commit/f19b6b8abdb4dfb64c804efee851dee0bb00471c))
- type errors ([5c49505](https://github.com/continuedev/continue/commit/5c49505f2071504481c17976670d883efebddb4a))
- type errors ([cdc9600](https://github.com/continuedev/continue/commit/cdc9600e689161877ff7416f63baa9dcbfa6c18a))
- type errors ([7ee4d8d](https://github.com/continuedev/continue/commit/7ee4d8db505924a4d0edd5ef3ac49f4bb9a706b6))
- type errors ([70ae585](https://github.com/continuedev/continue/commit/70ae58594cd136e16a53fcfde88a227dbec1104f))
- type errors ([34489b1](https://github.com/continuedev/continue/commit/34489b14de0fc1c5cfb300a624b3b8571372e532))
- type errors / formatting ([9dba473](https://github.com/continuedev/continue/commit/9dba4730b958e05ffad6e0bab74effefd228cd6c))
- types ([7b9c5cc](https://github.com/continuedev/continue/commit/7b9c5cc4f062e2882706919b1effe34a460f45b8))
- types ([82cad59](https://github.com/continuedev/continue/commit/82cad596c85d0ee0b17ceccdf1f6cca5811b8f94))
- types ([5faecca](https://github.com/continuedev/continue/commit/5faecca09f27a7490b4050b7191197c74fe38299))
- types ([be27552](https://github.com/continuedev/continue/commit/be27552e3330b3a18ecb1065b6ab7210a4375826))
- types ([4b120e5](https://github.com/continuedev/continue/commit/4b120e543e7c2cec0d33714cfebe88800604fbfd))
- types ([44212e4](https://github.com/continuedev/continue/commit/44212e45828bb72c035bf63de4c13342935dcf0f))
- types ([72b253d](https://github.com/continuedev/continue/commit/72b253d14faf2d19832b0307fb4fba1e9aed11dd))
- types ([a88f5d7](https://github.com/continuedev/continue/commit/a88f5d771ced366926c9d6d821c697e52847a2f9))
- types ([41a7916](https://github.com/continuedev/continue/commit/41a79166aadc2bbbe80145d12bfeb1336d94c7e2))
- typo ([be874ad](https://github.com/continuedev/continue/commit/be874addb463f7db81c43b52f6e69f9de1c11fa3))
- **typo:** customize overview doc ([d4dfb69](https://github.com/continuedev/continue/commit/d4dfb699af1f5bffc923a5d850f877ed45ad74a6))
- undefined in title ([dcdfa59](https://github.com/continuedev/continue/commit/dcdfa59db43ae4e864e5cb1ca04b6804cca64d2f))
- unnecessary dependency ([ade83fd](https://github.com/continuedev/continue/commit/ade83fd0c6b3f2d3bb8175b6bdbdc0a6e9735c5b))
- unnecessary scroll delay ([183ae03](https://github.com/continuedev/continue/commit/183ae03ee37ba6629201211e9a63fbcb04ebc527))
- unset system message ([4480ee3](https://github.com/continuedev/continue/commit/4480ee3ac358e60182304e0e052c27ae45815e2f))
- update adf2md package ([ecbc123](https://github.com/continuedev/continue/commit/ecbc1234f7419f7921b31354594e5841363c54f5))
- Update Amazon Bedrock documentation and categorization ([b156d8f](https://github.com/continuedev/continue/commit/b156d8fd4c103e520ad720853752a4f9033ef68f))
- update CodebaseIndexer path ([#1870](https://github.com/continuedev/continue/issues/1870)) ([00d19e6](https://github.com/continuedev/continue/commit/00d19e623f8abb04fc4a022d354a909e2087aa3c))
- update imports in CodeSnippetsIndex.ts ([434e4ee](https://github.com/continuedev/continue/commit/434e4ee44b513db15d502c6a9d52ceb17fa805c3))
- update install script ([91f87f8](https://github.com/continuedev/continue/commit/91f87f8fd0ca328a91c9fd033c58fd5d2db02892))
- update intellij getDiff to return string[] ([1672d9f](https://github.com/continuedev/continue/commit/1672d9f864e99436b8f309af3529f2fda341172d))
- update snippets index to upsert cmds ([676f0d9](https://github.com/continuedev/continue/commit/676f0d9335df7bf7f42aae3434847cc0032c3c0a))
- update spacing ([a9d6597](https://github.com/continuedev/continue/commit/a9d6597a3fa054d093cb63fa5859e24b8f8d571b))
- update streamId matching ([8a1ce0f](https://github.com/continuedev/continue/commit/8a1ce0f1f06e92b7bd140d7eabd8393dc5993fea))
- update streamResponse function to use streaming decoding ([#1436](https://github.com/continuedev/continue/issues/1436)) ([2d1155d](https://github.com/continuedev/continue/commit/2d1155d7d4441042480bf928398b7a15a58d92d2))
- uri utils ([f1026c6](https://github.com/continuedev/continue/commit/f1026c6a2cfdd9424097d2103a5ffc2216e402d9))
- use `cmd` for windows MCP connections ([b8ca4cb](https://github.com/continuedev/continue/commit/b8ca4cb6ee91670e87ae331f5e4bab24a42adbe6))
- use bolt icon for shortcuts ([ff2116b](https://github.com/continuedev/continue/commit/ff2116bacc4537e09792ce9817b68e9cdfacb93e))
- use dir hash in `tagToString` ([9c2a780](https://github.com/continuedev/continue/commit/9c2a780fa986f5f98c3231a12971d3f16c7a8e74))
- use exponential backoff when checking ollama ([ebbc2fd](https://github.com/continuedev/continue/commit/ebbc2fd55df1fc43e1eacceba042fa4c0bc38e77))
- use ide scheme auth redirect ([bae74c1](https://github.com/continuedev/continue/commit/bae74c1b324b51c2e9b14b548f92557c75025f51))
- use index when sending chat input ([9398945](https://github.com/continuedev/continue/commit/93989459e3806f1ba6d647036dc9db064bf30e38))
- use introduction layout ([2b71869](https://github.com/continuedev/continue/commit/2b7186973695703a48daacea46c076231d308ddf))
- Use MCP server_name from JSON config as display name ([481d60a](https://github.com/continuedev/continue/commit/481d60a74d9b7cd0492aa5875d89e5b4e2e00c51))
- use new edit for "Generate code" cmd ([aac6f80](https://github.com/continuedev/continue/commit/aac6f805eb35107e7d8754413d955ccfeb964576))
- use prompt file sys msg ([093225d](https://github.com/continuedev/continue/commit/093225d4fed4d5249ca506b9867f5b5305ca4003))
- use proper alternating colors in KeyboardShortcuts.tsx ([f1bfcec](https://github.com/continuedev/continue/commit/f1bfcec8257c2b3f99fa99a643b0464239600490))
- vitest ([3bc4bb7](https://github.com/continuedev/continue/commit/3bc4bb710cfb2d9ef340a61f7c0ca27cb2540a28))
- vitest ([ceeb87e](https://github.com/continuedev/continue/commit/ceeb87e81b8848517dfc604025f11bf46e1955b0))
- **vscode:** handle null values for edits and page in getSidebarContent ([#618](https://github.com/continuedev/continue/issues/618)) ([f8a02ad](https://github.com/continuedev/continue/commit/f8a02add1775805246410ee3df396a4028ff2ec9))
- **vscode:** improve forceAutocomplete command reliability ([cc50768](https://github.com/continuedev/continue/commit/cc507687eb36be5b7fd59eac433a9a908c1ee07e))
- **vscode:** keybinding full screen toggle shortcut ([#625](https://github.com/continuedev/continue/issues/625)) ([2921504](https://github.com/continuedev/continue/commit/292150476c1cfcace2f6434d11c3a2dab1e5ccdb))
- wait for permissions before initializing messenger ([7d16b8a](https://github.com/continuedev/continue/commit/7d16b8a870ebf5348713fb116c109a646fe7c79e))
- wait for sidebar before other commands ([1566adc](https://github.com/continuedev/continue/commit/1566adcaeb3bc5258a563b208a511cdfb3938d7a))
- watcher ([f41c819](https://github.com/continuedev/continue/commit/f41c8194fc11da9c76de0531cccd73df78a4b1c2))
- watcher ([be153f8](https://github.com/continuedev/continue/commit/be153f8ab966e210d6249ff179c4ec1580507d88))
- whitespace in new line completions ([4db2637](https://github.com/continuedev/continue/commit/4db2637d5b65d427e959b7fd607a65ce4140c355))
- workflow ([e5fb4f1](https://github.com/continuedev/continue/commit/e5fb4f17ad9d3b6e72d6c7e0920357e607c0e776))
- wrap editor buttons when out of space ([#1727](https://github.com/continuedev/continue/issues/1727)) ([b38ec0e](https://github.com/continuedev/continue/commit/b38ec0e6153e9077d1e39dfd7a453746da31c75b))
- wrong shortcut for JB ([b358be2](https://github.com/continuedev/continue/commit/b358be2cfb798bfb6646f5015117199e2ad3a6e2))

### Features

- :adhesive_bandage: ca_bundle_path for maybeproxyopenai ([1018cd4](https://github.com/continuedev/continue/commit/1018cd47306f95dde35e1a0cc6b2a830444af389))
- :adhesive_bandage: QueuedLLM for simultaneous reqs (LM Studio) ([e9d2891](https://github.com/continuedev/continue/commit/e9d289173ec28a1a90ae58b1834c476bb46834b8))
- :art: custom prompt templates per model ([2e69e11](https://github.com/continuedev/continue/commit/2e69e117e198698f57bda06794cf030afbfe69e9))
- :art: small changes to /codebase ui ([1b68904](https://github.com/continuedev/continue/commit/1b689046a7f9323c7bd56e14d403675db0b38d54))
- :bricks: Enable terminals for additional vscode Remote Host Types ([1500281](https://github.com/continuedev/continue/commit/1500281bb547f7308aa7316f6783072e4f81fbc1))
- :bug: kill old server if needed ([a34046b](https://github.com/continuedev/continue/commit/a34046bbbe817f81cd6d8b7ff9025413589571aa))
- :children_crossing: display troubleshooting link when loading ([698dccf](https://github.com/continuedev/continue/commit/698dccf474619963de0312d36af6d01e3df8b47a))
- :children_crossing: drag continue to right bar tip ([04b1fde](https://github.com/continuedev/continue/commit/04b1fde6ea77e7698870063c3f588da93d763544))
- :children_crossing: keep file context up to data by listening for filesystem events ([#396](https://github.com/continuedev/continue/issues/396)) ([b6435e1](https://github.com/continuedev/continue/commit/b6435e1e479edb1e4f049098dc8522e944317f2a))
- :children_crossing: more keyboard shortcuts ([bd202df](https://github.com/continuedev/continue/commit/bd202df41755c581844d0ab1773ba55968b15450))
- :children_crossing: sort history by reverse date ([fd77a4b](https://github.com/continuedev/continue/commit/fd77a4bd6b255260d0f4cad11947b38f4d30030e))
- :children_crossing: tip to debug ([ec74169](https://github.com/continuedev/continue/commit/ec741697c42745d29539be08bc3e01dcd86b1643))
- :construction: create new sessions ([19060a3](https://github.com/continuedev/continue/commit/19060a30faf94454f4d69d01828a33985d07f109))
- :construction: first work on URLContextProvider ([6467759](https://github.com/continuedev/continue/commit/6467759012a139e76dcf022a681355f7d310a30d))
- :construction: react-router-dom work ([31e7c98](https://github.com/continuedev/continue/commit/31e7c9828f985eceb16b4c9c749cc5d4d9fd5beb))
- :construction: Router and new history page ([f19345c](https://github.com/continuedev/continue/commit/f19345c652cfcf1bdf13d0a44a2f302e0cd1aa4c))
- :construction: successfully loading past sessions ([c255279](https://github.com/continuedev/continue/commit/c25527926ad1d1f861dbed01df577e962e08d746))
- :construction: work on EditableDiv - better ctx prov. UI ([e33d579](https://github.com/continuedev/continue/commit/e33d579a1d2b643842827925d032c3de92cf5217))
- :egg: getting to know users form ([5dcdcd8](https://github.com/continuedev/continue/commit/5dcdcd81da2050825212e216bf5e7e69678d8c6e))
- :fire: fix duplication in reference ([1e3c8ad](https://github.com/continuedev/continue/commit/1e3c8adabba561eeef124144f3a2ef36d26334b4))
- :globe_with_meridians: alpaca chat template ([568771d](https://github.com/continuedev/continue/commit/568771d9b94280f1cb47aae863e8faf168eb052b))
- :green_heart: ship with binary ([2751dde](https://github.com/continuedev/continue/commit/2751ddeb2dd8150a0d7de6c5b65e275e1aa0e155))
- :lipstick: add context button (plus icon) ([2b35e5f](https://github.com/continuedev/continue/commit/2b35e5f5cff948ca7d4f207b23db68f0da248a95))
- :lipstick: add textgenwebui as official option in Ui ([09ac7a7](https://github.com/continuedev/continue/commit/09ac7a7fc07d915ac6f3ef96c8e8d1894b7719b9))
- :lipstick: better loading experience ([e19c918](https://github.com/continuedev/continue/commit/e19c918bb1c517a6a119ae8437c46e0724d2be9d))
- :lipstick: fixed footer and change button color ([48b8f1f](https://github.com/continuedev/continue/commit/48b8f1f0ad89a2b4e35f49c360576dd5aa99a7c0))
- :lipstick: gallery banner color ([0647a43](https://github.com/continuedev/continue/commit/0647a43a24c50ff0e52f23c49d979bddfcfbcd87))
- :lipstick: handful of UI improvements ([ceafdf1](https://github.com/continuedev/continue/commit/ceafdf18c9d9f0f8769d4a9e45c8a407179161c5))
- :lipstick: improvements to keyboard shortcuts ([7bf8e5b](https://github.com/continuedev/continue/commit/7bf8e5b56a518979bc1d2602b8eb4a2caf2b5fdb))
- :lipstick: more ui improvements ([f9a84bd](https://github.com/continuedev/continue/commit/f9a84bd2d65b3142cbcfcdd8e1e9394c9d4b458e))
- :lipstick: more ui improvements ([6e3ff01](https://github.com/continuedev/continue/commit/6e3ff0173e79f5374da9962c964559e0fb7165f5))
- :lipstick: nested context provider dropdown ([8d423fd](https://github.com/continuedev/continue/commit/8d423fd8d1d5b136e8138a906e8594ab93ec1982))
- :lipstick: nicer "continue server loading" UI ([8070ce1](https://github.com/continuedev/continue/commit/8070ce17c6d666436df38c684f5868ee7f689422))
- :lipstick: query input indicator for ctx provs ([4362a51](https://github.com/continuedev/continue/commit/4362a51214a683bfe1efd424ddb226d4e636eeed))
- :lipstick: setting to change font size ([4ea1760](https://github.com/continuedev/continue/commit/4ea176007d2228364d4d3fa4519898047cef988f))
- :lipstick: small ui tweaks, detached child process ([0181d62](https://github.com/continuedev/continue/commit/0181d6236d8b74c80adb62648fd6571431cf3210))
- :lipstick: sticky top bar in gui.tsx ([ef86d66](https://github.com/continuedev/continue/commit/ef86d661d54295c1abb9712557080f1838f96b33))
- :lipstick: UI Improvements! ([ae058c6](https://github.com/continuedev/continue/commit/ae058c6bac7ea37108e2894e419a22dfb95fd3ff))
- :lipstick: update icon and description ([92e7c9b](https://github.com/continuedev/continue/commit/92e7c9bb627a5559769e0eca23e02e106d2cfe96))
- :lipstick: update marketplace icon for pre-release ([cc98ad8](https://github.com/continuedev/continue/commit/cc98ad86d561d26c97dfdb24607a1d70afbcd2a1))
- :loud_sound: add context to dev data loggign ([8ac1518](https://github.com/continuedev/continue/commit/8ac15184aaa30d13bf168ff5123a12fb7a2dd39f))
- :loud_sound: display any server errors to the GUI ([daabebc](https://github.com/continuedev/continue/commit/daabebcc5d6df885a508582c0ca13e659305d2ff))
- :loud_sound: fallback unique id when vscode returns someValue.machineId ([c479442](https://github.com/continuedev/continue/commit/c47944260c5600e49d83568b3c4bafa3b7c2a37e))
- :loud_sound: give users access to Continue server logs ([5d97349](https://github.com/continuedev/continue/commit/5d973490687c40922f2b7a2ddf2a3e19c207eb0f))
- :loud_sound: light telemetry or context providers ([2959042](https://github.com/continuedev/continue/commit/2959042fa5a940aa4e8851b9d4db91f0f86092ff))
- :loud_sound: telemetry for vscode vs. jetbrains ([257cef6](https://github.com/continuedev/continue/commit/257cef697c93d2f2f59936587834908bd69ae842))
- :memo: embeddings experimental walkthrough ([e1ce1fe](https://github.com/continuedev/continue/commit/e1ce1fefee6a3f4c17ac568ba87cb7a4bcf65795))
- :memo: note about where session data is stored ([8ada89b](https://github.com/continuedev/continue/commit/8ada89b0f66f9e746394ee64591359537fe0c7f0))
- :money_with_wings: free trial usage indicator ([354a3f4](https://github.com/continuedev/continue/commit/354a3f493074b1fb63ff4f206a94c35f05673e99))
- :mute: complete removal of telemetry when allow_anonymous_telemetry false ([ae7dffa](https://github.com/continuedev/continue/commit/ae7dffa211af209aea2ca13b37729e390047dd7c))
- :necktie: allow timeout param for OpenAI LLM class ([404f7f8](https://github.com/continuedev/continue/commit/404f7f8089190d04c05957dc653baff44f342dc7))
- :recycle: load preset_urls at load_index ([3dabc4b](https://github.com/continuedev/continue/commit/3dabc4bd6c72e2d12afb059040ca75f606e47d9d))
- :rocket: headers param on LLM class ([e16b8ff](https://github.com/continuedev/continue/commit/e16b8ff5f2d9187f2b207addc1cd70d0cacbf9c8))
- :sparkles: [@terminal](https://github.com/terminal) context provider ([40cfabb](https://github.com/continuedev/continue/commit/40cfabb8ce8afe20e51ca4bafddc6a0b4755bf2c))
- :sparkles: /cmd slash command ([011877c](https://github.com/continuedev/continue/commit/011877c09e88ffcc715defc33e5c74c71ccc8aea))
- :sparkles: /share slash command ([4e38043](https://github.com/continuedev/continue/commit/4e3804351b76cc763d904f572ad525b651d8bc00))
- :sparkles: add edit templates to model packages ([1de976a](https://github.com/continuedev/continue/commit/1de976a6a11a0b945d59800b5a58f354808a49fc))
- :sparkles: add max_tokens option to LLM class ([ff2a397](https://github.com/continuedev/continue/commit/ff2a3978a1e2c95a4e288b56411bf0c32b86757b))
- :sparkles: add searchcontextprovider to default_config.py ([64f41fc](https://github.com/continuedev/continue/commit/64f41fc8a0a2616fe7074d0a49e7642fd462c95d))
- :sparkles: add stop_tokens option to LLM ([a16ba7a](https://github.com/continuedev/continue/commit/a16ba7a0166dbf9062ee4616e3ccfbff377e9f4b))
- :sparkles: Add support for Claude Sonnet 4 ([b24a76f](https://github.com/continuedev/continue/commit/b24a76fdabb5eccada838d8fe5ed5834c0120df1))
- :sparkles: add urlcontextprovider back to default config ([570891e](https://github.com/continuedev/continue/commit/570891e0201769defeabec95a58c997f6d5f3889))
- :sparkles: allow AzureOpenAI Service through GGML ([1343d12](https://github.com/continuedev/continue/commit/1343d1227cc86c860fb12695e64eaeae1384f72a))
- :sparkles: allow changing the summary prompt ([5c8b28b](https://github.com/continuedev/continue/commit/5c8b28b7fddf5b214de61102c768ef44d4087870))
- :sparkles: allow custom OpenAI base_url ([cb0c815](https://github.com/continuedev/continue/commit/cb0c815ad799050ecc0abdf3d15981e9832b9829))
- :sparkles: alt+cmd+d to automatically debug terminal! ([d0483ba](https://github.com/continuedev/continue/commit/d0483ba15b4ad13399a3385ae351cf33cca3db7f))
- :sparkles: auto-reload for config.py ([e652e90](https://github.com/continuedev/continue/commit/e652e90806b84eb409c496dd0903a4817243edc2))
- :sparkles: autoscrolling ([e6d369f](https://github.com/continuedev/continue/commit/e6d369f4312f0c8d175251e149c62d08608cb18c))
- :sparkles: back button on history page ([aafa5d5](https://github.com/continuedev/continue/commit/aafa5d5ec91a533f70d644e4d3fadd6f388c3e4b))
- :sparkles: change proxy url for openai class ([32a9a47](https://github.com/continuedev/continue/commit/32a9a477d33acd5cdde08164ebeb355b27a656b5))
- :sparkles: codelens in config.py ([58cd4db](https://github.com/continuedev/continue/commit/58cd4db2534aba9fed98925e68dc342efbc54fb0))
- :sparkles: Continue Quick Fix ([9af39a6](https://github.com/continuedev/continue/commit/9af39a67829a6770b93ffdaa6ea70af3125c7daf))
- :sparkles: Continue Quick Fix ([52cd93a](https://github.com/continuedev/continue/commit/52cd93ad73f7df6a5140b7d629e4f6e473dc0380))
- :sparkles: delete context groups ([2d3d96e](https://github.com/continuedev/continue/commit/2d3d96e5b55a225eb97251850909eb7a0a7242ed))
- :sparkles: diff context provider ([99db0da](https://github.com/continuedev/continue/commit/99db0da9d68c64d0b5adcab21e07c2db438c2404))
- :sparkles: display model params for previous prompts ([b1b30a4](https://github.com/continuedev/continue/commit/b1b30a459cbd589a471e1528ebfa9aaeb0514968))
- :sparkles: edit previous inputs ([c6a3d8a](https://github.com/continuedev/continue/commit/c6a3d8add014ddfe08c62b3ccb1b01dbc47495f5))
- :sparkles: EmbeddingContextProvider ([c6a1255](https://github.com/continuedev/continue/commit/c6a12550ffca1ffe35630e7aa9af6913ddbe0675))
- :sparkles: FileTreeContextProvider ([8bd76be](https://github.com/continuedev/continue/commit/8bd76be6c0925e0d5e5f6d239e9c6907df3cfd23))
- :sparkles: filter history by workspace ([0757bd2](https://github.com/continuedev/continue/commit/0757bd2b556996b9c434ac43e3e4a3b042ef5802))
- :sparkles: Give the terminal color and ansi escape rendering ([e83290b](https://github.com/continuedev/continue/commit/e83290bd3a921929052b0c0c91751800e7e9fd2c))
- :sparkles: highlight code on cmd+shift+L ([a1fdf16](https://github.com/continuedev/continue/commit/a1fdf164b776c5ff4ddfa1c4cad66e41de4254c0))
- :sparkles: huggingface inference api llm update ([bbf7973](https://github.com/continuedev/continue/commit/bbf7973ec091823c4197d59daaf151b748ee52fc))
- :sparkles: huggingface tgi LLM class ([a0e2e2d](https://github.com/continuedev/continue/commit/a0e2e2d3d606d8bf465eac541a84aa57316ee271))
- :sparkles: improved edit prompts for OS models ([1785e92](https://github.com/continuedev/continue/commit/1785e92f118b855f4a655d9b617d54b5c857a6ef))
- :sparkles: improved model dropdown ([2f792f4](https://github.com/continuedev/continue/commit/2f792f46026a6bb3c3580f2521b01ecb8c68117c))
- :sparkles: improvement to @ search rankings ([5590f63](https://github.com/continuedev/continue/commit/5590f63e42fda38d780bdc390361a65b65576498))
- :sparkles: llama-2 support ([72d18fb](https://github.com/continuedev/continue/commit/72d18fb8aaac9d192a508cd54fdb296321972379))
- :sparkles: LlamaCpp LLM subclass ([fc9e8e4](https://github.com/continuedev/continue/commit/fc9e8e4e325782409258dd483e36abf441051ee6))
- :sparkles: LSP connection over websockets ([a6d21f9](https://github.com/continuedev/continue/commit/a6d21f979fce6135fd76923478f76000b1b343cf))
- :sparkles: make follow-up edits ([73ae5d3](https://github.com/continuedev/continue/commit/73ae5d306c16d7c372e831d3ca41067a62c8481f))
- :sparkles: Make the terminal command aware of its OS, platform and shell ([1a786f6](https://github.com/continuedev/continue/commit/1a786f605da47abdfc66b02f4e88044ca95df960))
- :sparkles: manually running server option ([29940ea](https://github.com/continuedev/continue/commit/29940ea4223194cc32f6324534ad75db9e39305a))
- :sparkles: more model options, ollama error handling ([e2a7d4a](https://github.com/continuedev/continue/commit/e2a7d4a3c7832f8788feccf4168c13ec87a31fb2))
- :sparkles: ollama LLM class ([402883e](https://github.com/continuedev/continue/commit/402883e0661c24c418fb5aa93832c6f62dc97a63))
- :sparkles: refactor via search Step ([9cd249e](https://github.com/continuedev/continue/commit/9cd249ee007911037639281c6d7590889ad7b467))
- :sparkles: run continue immediately from pypi pkg ([70f6da9](https://github.com/continuedev/continue/commit/70f6da9b1ad190a967974fb477db669cbb5c928f))
- :sparkles: saved context groups ([c98f860](https://github.com/continuedev/continue/commit/c98f860460767fe14f8fbf139150b1bd1ee2ff12))
- :sparkles: Search context provider with ripgrep ([4b9dd4c](https://github.com/continuedev/continue/commit/4b9dd4cf8e853e17d92fb76fc726260d79e4bd7a))
- :sparkles: select custom model to use with edit step ([c1e5039](https://github.com/continuedev/continue/commit/c1e5039731941eb6b6eea166edd433cd49d4e858))
- :sparkles: select model from dropdown ([044b7ca](https://github.com/continuedev/continue/commit/044b7caa6b26a5d78ae52faa0ae675abc8c4e161))
- :sparkles: select model param count from UI ([105afec](https://github.com/continuedev/continue/commit/105afeccca903072bc48772bdaf8f100f996c4a7))
- :sparkles: set session timeout on GGML requests ([d04eec7](https://github.com/continuedev/continue/commit/d04eec7ee97319a6bcc48d289cd6eb3e0d9b8e19))
- :sparkles: set static urls for contextprovider ([d2b1aed](https://github.com/continuedev/continue/commit/d2b1aedcedf950d792baee202efdab199b05e57e))
- :sparkles: support browser-based IDEs with createMemoryRouter ([c9d96be](https://github.com/continuedev/continue/commit/c9d96be5615b9d193a1eeff9ab00da7ca0fe0b6b))
- :sparkles: support for Together.ai models ([8456b24](https://github.com/continuedev/continue/commit/8456b24318b13ea5d5dabec2328dd854f8a492b4))
- :sparkles: support stablecoder with replicate LLM ([d5e8688](https://github.com/continuedev/continue/commit/d5e86883f05fe3e99e1d6ff64241a48f935cc927))
- :sparkles: testing improved prompting for stablecode ([6112f26](https://github.com/continuedev/continue/commit/6112f26888086ccd47ca6bcfefdbc5b82ea86879))
- :sparkles: testing in ci, final test of ([cbd7656](https://github.com/continuedev/continue/commit/cbd7656bb4c9aebfe98c746111af52cf7192aa1b))
- :sparkles: text-gen-webui, cleaning config and welcome ([36d517e](https://github.com/continuedev/continue/commit/36d517e37d87b1bd39d6027577714b60c32e81e9))
- :sparkles: verify_ssl option for all LLMs ([e0522b9](https://github.com/continuedev/continue/commit/e0522b92cfa80491718de07928ce6a31850dab70))
- :technologist: bit of customization for DefaultPolicy ([3966790](https://github.com/continuedev/continue/commit/396679009fef21e13c1a6095212d1bd68e7f2a86))
- :technologist: button to view logs when loading ([b83eb52](https://github.com/continuedev/continue/commit/b83eb52c98d637ab3e3becf98aed9899821ea00d))
- :technologist: github workflow to measure issue/PR stats ([f75c423](https://github.com/continuedev/continue/commit/f75c42332c44c0d2a1a7e7a7ea32c2ef346df609))
- :white_check_mark: update test and add model telemetry ([3541d6a](https://github.com/continuedev/continue/commit/3541d6a770c919e1f2e55a1ae53c4fc3abe31aa7))
- :wrench: update default config.py imports ([e2d0baf](https://github.com/continuedev/continue/commit/e2d0baf39348597bdd1015897152f4e3bee0744d))
- :zap: queue messages before load, then send ([4c8b561](https://github.com/continuedev/continue/commit/4c8b56135b0a1862a4f1984e80aa1409f15e177d))
- :zap: reduce default max_tokens ([8fff1a8](https://github.com/continuedev/continue/commit/8fff1a811c477874482b65d014e4c5565d4a8649))
- `create_rule_block` tool ([2c8032c](https://github.com/continuedev/continue/commit/2c8032c3397f6ac8c26b8ce5e5b2fe89d079d3da))
- `description` in markdown yaml ([e4b70db](https://github.com/continuedev/continue/commit/e4b70dbfd843345ae0689a55802462473f5641dc))
- `globs` on rules and block docs ([24e22db](https://github.com/continuedev/continue/commit/24e22db9d416be54426709948564aee4aa08d54e))
- `requestRule` tool ([af30fbe](https://github.com/continuedev/continue/commit/af30fbe360def278cd56858d88c5373c80a2dab6))
- add "Gathering context..." indicator ([dd865ea](https://github.com/continuedev/continue/commit/dd865eadea014ccc8588a49e6d9c338d89734c9d))
- add "onboarding" slash command ([#1961](https://github.com/continuedev/continue/issues/1961)) ([5819ffb](https://github.com/continuedev/continue/commit/5819ffb43901eec8ad6af85737c28896f20c4e6e))
- add `globs` to create rule tool ([047e4e4](https://github.com/continuedev/continue/commit/047e4e418b7f252cb303f19992025acc4fb79d4f))
- add `index.ts` to sdk ([ae31c54](https://github.com/continuedev/continue/commit/ae31c54011ac365e560cb02b9851a6806641503a))
- add `no-negated-condition` eslint ([eb7d67d](https://github.com/continuedev/continue/commit/eb7d67dcf1a338c473a1bb7f787e4f35e62f8f73))
- add `stream` in defaultCompletionOptions yaml ([f72e293](https://github.com/continuedev/continue/commit/f72e293e2f6251e1cca632dffa7ec14554016a73))
- add `tsc:watch` cmd to vs code ([1d5e164](https://github.com/continuedev/continue/commit/1d5e164325e8fe8e66759baf2ac47ce766355faf))
- add accept/reject all btns ([011e2c2](https://github.com/continuedev/continue/commit/011e2c264407ff72a0bc90980ec7d8250da4bbd6))
- add agentinteraction dev data ([971c4de](https://github.com/continuedev/continue/commit/971c4de33e1a3f117eafe9d64bf175c0d2a849ef))
- add animated ellipsis to lump ([e14163c](https://github.com/continuedev/continue/commit/e14163cda2ff1de2e90621f1de2341fd90b136d2))
- add API key support for TEI reranker ([5dc25b5](https://github.com/continuedev/continue/commit/5dc25b5eb3b1f914d106a18bbb34f7d28e3039f3))
- add azure provider config ([#1764](https://github.com/continuedev/continue/issues/1764)) ([c9635de](https://github.com/continuedev/continue/commit/c9635def237e0bb4e1d899057e6b651b6a6cd1b2))
- add best experience onboarding ([8b30504](https://github.com/continuedev/continue/commit/8b305046eff820999e805c8b2cd6400e3572da1b))
- add chat scrollbar visibility configuration support ([14eaf32](https://github.com/continuedev/continue/commit/14eaf3272cde744ab6390d7113c2cb507e0d6734))
- add Claude 3.7 support to toolSupport.ts ([50ad91b](https://github.com/continuedev/continue/commit/50ad91bced864272c7f1bb92c13d7c910a0b6be2))
- Add cloudflare as provider ([ad8743a](https://github.com/continuedev/continue/commit/ad8743a9b563ae6e09cc140a0b3f9f202715e3b5))
- add code that comment is based on ([e3653f9](https://github.com/continuedev/continue/commit/e3653f98f08e52fa6a55d41ca147add1c8e5515a))
- add combobox for edit file selection ([7c6295f](https://github.com/continuedev/continue/commit/7c6295feeb32a478a2d8867f0a2b863583f6a97e))
- add deepseek models for novita ai ([a31c3eb](https://github.com/continuedev/continue/commit/a31c3ebc8334f931281cdb0a348150347c99d0e6))
- Add docs/getDetails endpoint in JetBrains ([da2dc1e](https://github.com/continuedev/continue/commit/da2dc1ed7de3caa9ae1e8b02a09526f57dd80bb8))
- add example integration test ([781a792](https://github.com/continuedev/continue/commit/781a792a5939958d05fa316261190c164871c563))
- add exponential backoff for API requests ([12ffa95](https://github.com/continuedev/continue/commit/12ffa956571acd12b383ba73560d3c42a7a4415c))
- add file search to quick edit ([#1714](https://github.com/continuedev/continue/issues/1714)) ([21d1b0c](https://github.com/continuedev/continue/commit/21d1b0c16dd9cd454d543e4b387f873e54d89aa5))
- add fixed version of the nodejs ([cd35857](https://github.com/continuedev/continue/commit/cd35857daf5577f4677f6675344fa8488e624352))
- add free trial card to onboarding ([#1600](https://github.com/continuedev/continue/issues/1600)) ([9bae5a2](https://github.com/continuedev/continue/commit/9bae5a254df25d25dc848b5c7bf69fc7189e6461))
- add gif to tutorial card ([e03eb9d](https://github.com/continuedev/continue/commit/e03eb9d5801985d1968d0528dd76767bf3907cbf))
- add gitlab context class ([efc1f3b](https://github.com/continuedev/continue/commit/efc1f3b9216d5bf503ecb58ff956f26a2bc4a130))
- add icon for URL ctx item peek ([e901cfd](https://github.com/continuedev/continue/commit/e901cfdf4ae383a2337c2e20182f6eee6d3010f9))
- add Jira context provider ([#860](https://github.com/continuedev/continue/issues/860)) ([8ba15b1](https://github.com/continuedev/continue/commit/8ba15b16665be871e037ada88d51b3403a8d094e))
- add Llama 3.1 8B to cloudflare provider options ([#1811](https://github.com/continuedev/continue/issues/1811)) ([bccbff2](https://github.com/continuedev/continue/commit/bccbff273c176c54f0209c5927a19b8c6d9375f9))
- add Moonshot AI model provider support ([b8a278d](https://github.com/continuedev/continue/commit/b8a278d216db9851aa2b0772e0313ff4bc12c7cc))
- add novita info ([94d8d39](https://github.com/continuedev/continue/commit/94d8d39cfc8d21f2544633330370d99ef17e0fdf))
- Add num_treads to ollama along with docs ([#863](https://github.com/continuedev/continue/issues/863)) ([d71721d](https://github.com/continuedev/continue/commit/d71721d71540cb084d67559e850440129faa67c0))
- add openai wrapper for sdk ([ab9a248](https://github.com/continuedev/continue/commit/ab9a248572a46e00f0fa6441af861dd260c4cb65))
- add OpenAI, xAI, Replicate and free-trial model options to config schema ([89a90f9](https://github.com/continuedev/continue/commit/89a90f99baaf493f184a09aa6083a606076f9771))
- Add prompt caching support ([24f9960](https://github.com/continuedev/continue/commit/24f9960e27ad065fec611661e69726c9be589042))
- add proxy support for ripgrep download ([d42c050](https://github.com/continuedev/continue/commit/d42c050e3645b4eb4635dd5a73ae12107adca701))
- add Quick Actions CodeLens feature ([#1674](https://github.com/continuedev/continue/issues/1674)) ([fdf3408](https://github.com/continuedev/continue/commit/fdf3408e0c9c2df749b5775d3c906abfdf40e799)), closes [#1536](https://github.com/continuedev/continue/issues/1536) [#1456](https://github.com/continuedev/continue/issues/1456) [#1564](https://github.com/continuedev/continue/issues/1564) [#1576](https://github.com/continuedev/continue/issues/1576) [#1570](https://github.com/continuedev/continue/issues/1570) [#1582](https://github.com/continuedev/continue/issues/1582) [#1600](https://github.com/continuedev/continue/issues/1600) [#1618](https://github.com/continuedev/continue/issues/1618) [#1626](https://github.com/continuedev/continue/issues/1626) [#1637](https://github.com/continuedev/continue/issues/1637)
- add Qwen2.5-Coder support ([387a76a](https://github.com/continuedev/continue/commit/387a76aa5bab873565de25a2d269f0b5b1a53f1e))
- add redux state for card logic ([5f32924](https://github.com/continuedev/continue/commit/5f32924becd3e8b03d19ffb4a5e4a2dcbfb6dbc3))
- add redux state for mfe ([6e5236a](https://github.com/continuedev/continue/commit/6e5236a58a896ab78faa1672e56237b4f4684877))
- add render util function ([413530a](https://github.com/continuedev/continue/commit/413530a029e32ab8e77520c0e5265cd37a84b24c))
- add rich quick pick for quick edit ([#1706](https://github.com/continuedev/continue/issues/1706)) ([f3b15eb](https://github.com/continuedev/continue/commit/f3b15eb1b14dcc3f4c28ebfb95150b0d6627cecb))
- add scope selector ([b78fdc4](https://github.com/continuedev/continue/commit/b78fdc4147e1ccb388b900a65099a2c61ba6ca26))
- Add signature column to code_snippets table ([a535e9e](https://github.com/continuedev/continue/commit/a535e9e15fb3041fec732574636221e0a83dccfa))
- add slash command cmd ([d4e1609](https://github.com/continuedev/continue/commit/d4e1609d2a65aeeece6caa8620a1b45196f40c98))
- add support for `baseSystemPrompt` ([f6957bb](https://github.com/continuedev/continue/commit/f6957bb856ba5915ad6351a518dc4b4a47536947))
- Add support for Cloudflare AI Gateway ([#1425](https://github.com/continuedev/continue/issues/1425)) ([837ad1c](https://github.com/continuedev/continue/commit/837ad1c552aef8909b9a1dbe01204ed2571134d7))
- add support for custom headers in SSE transport ([d392d77](https://github.com/continuedev/continue/commit/d392d7736d5d31804cd54b3c307bbfb6289e5358))
- add support for Mistral models in toolSupport.ts ([928cf2c](https://github.com/continuedev/continue/commit/928cf2c73752a23fe3fc026bbf3957c5c80d5604))
- add support for multiple MCP server types (stdio, sse, websocket) in the YAML configuration schema, ensuring backward compatibility with legacy configurations ([63a2ffe](https://github.com/continuedev/continue/commit/63a2ffe9696d90786f030c2af02cec2c463d914e))
- add tests ([0819bec](https://github.com/continuedev/continue/commit/0819becfd98127a187cc84484dd59bb4348a1ec3))
- add tutorial card ([#1716](https://github.com/continuedev/continue/issues/1716)) ([cb8b207](https://github.com/continuedev/continue/commit/cb8b207582dec6ce997c2c602c9998a5a64504db))
- add unified diff instant apply ([3c36b4d](https://github.com/continuedev/continue/commit/3c36b4dc378ddd8c5c5c2cc234e9a605d776d928))
- add Vertex AI support ([dfaa02c](https://github.com/continuedev/continue/commit/dfaa02c5051c44303e055675324ff6eaaac0e91b))
- allow input to exit ([18c5019](https://github.com/continuedev/continue/commit/18c50195248f6ca6c64e1eeb3cb8f10e6b85ffb5))
- allow JetBrains users to index docs ([#1797](https://github.com/continuedev/continue/issues/1797)) ([e0079a4](https://github.com/continuedev/continue/commit/e0079a43721f6002ff59e2eaa79dc8768a6f66f5))
- allow users to skip local onboaridng ([4685db0](https://github.com/continuedev/continue/commit/4685db02b854ab0a598defe0fdc4a68f8de50cc2))
- apply e2e tests ([12b3ed9](https://github.com/continuedev/continue/commit/12b3ed9291b4f46e920f8417f1e6585e45b7edd9))
- apply if state is active, onEnter callback dont working ([fed1651](https://github.com/continuedev/continue/commit/fed165161d1610db282b52230a4d914f4d2ffa62))
- apply waiting cursor to chat-input enter-button during prompting ([6d08361](https://github.com/continuedev/continue/commit/6d0836189cd1c18cf97b1e604adf4e5f201665b9))
- assistant select ([3b539d5](https://github.com/continuedev/continue/commit/3b539d53c39a98c361e27db6dfb72a8e14ddae50))
- assistant select refresh on right ([04f5e3e](https://github.com/continuedev/continue/commit/04f5e3e165fddf3101e6210b1111c0f261793054))
- **autocomplete:** recently visited ranged service ([811507a](https://github.com/continuedev/continue/commit/811507a406ed543adb4a8f876c3c3f30d8a9be35))
- better buttons for account dialog ([2a4c69f](https://github.com/continuedev/continue/commit/2a4c69ff87b64e21ad04edfed2aad7b048833b53))
- better editor content handling in edit ([7c72c4a](https://github.com/continuedev/continue/commit/7c72c4af878e4b683b582e40f483551314e92873))
- better error handling around ripgrep ([22f6fdf](https://github.com/continuedev/continue/commit/22f6fdfa1be6f84ab8548155465f863cb65a7e30))
- better type names ([43ebb1a](https://github.com/continuedev/continue/commit/43ebb1ad0a95ac1dfc0ff1e198fe53e8c50f0671))
- bookmark first 5 prompts by default ([0909ef9](https://github.com/continuedev/continue/commit/0909ef938b6d2928fa650c0e0613c1e106a0315d))
- brand text fix ([a25afee](https://github.com/continuedev/continue/commit/a25afeeda10c86072f80c025c20c21b167ddaaf0))
- bugfixes on redux schema updates ([4efd661](https://github.com/continuedev/continue/commit/4efd66137592916167f562ac45533dfffacd35cc))
- build steps for config-yaml ([73b95f3](https://github.com/continuedev/continue/commit/73b95f314703c266d9e348f9c1ba3dbbb3f3d266))
- cache org selection results ([13d5668](https://github.com/continuedev/continue/commit/13d566831db9e66ae270ff70e2b5c63304df6458))
- cache org selection results ([6e81720](https://github.com/continuedev/continue/commit/6e81720186d4215d6a650835f72ed1c6cc20e28f))
- capture base class and interfaces implemented ([df2bbc8](https://github.com/continuedev/continue/commit/df2bbc89da7f7f9ee406a8fe6d0c4fd244abd488))
- capture python definitions ([2f84ef0](https://github.com/continuedev/continue/commit/2f84ef0ab9a87aa6300415d30db02e3460a07195))
- change model desc ([8d3ecaf](https://github.com/continuedev/continue/commit/8d3ecafa3dbd9a3373ec7784eda1feac6b77b684))
- check for `nvm` version in install script ([b1a93b7](https://github.com/continuedev/continue/commit/b1a93b7a2dd22ab82ef9acdec5f1096c33c44c1b))
- cleanup apply manager ([4b9bac5](https://github.com/continuedev/continue/commit/4b9bac55ffae743d0221df65f0a14d0981f74aeb))
- cleanup inline edit code ([90d895e](https://github.com/continuedev/continue/commit/90d895e2e22ee40033c524a109b5777b5c3bf6cd))
- Client Certificate Options Support ([#1658](https://github.com/continuedev/continue/issues/1658)) ([136bf9e](https://github.com/continuedev/continue/commit/136bf9e0f0ce0193f64fe291922431808c32406e))
- close files ([d1eca9d](https://github.com/continuedev/continue/commit/d1eca9deeb3c1550982f679a58265593af603453))
- close tutorial listener ([d4d3102](https://github.com/continuedev/continue/commit/d4d3102b61c800576513cbb07c9d482fe973a0ef))
- collapsed codeblock by default ([979c247](https://github.com/continuedev/continue/commit/979c2473c61747157e65e1dcd33fd73ccfe404ad))
- config.json validation ([429e54e](https://github.com/continuedev/continue/commit/429e54ed347446747d206a97cb80c91baf5d407e))
- configure docs through config.json ([#1864](https://github.com/continuedev/continue/issues/1864)) ([d7dbdff](https://github.com/continuedev/continue/commit/d7dbdfff485f3970b8595c5a2680a012443747a5))
- consolidate StepContainer ([58d9eb7](https://github.com/continuedev/continue/commit/58d9eb7a7e8102940d59f26a65e01edb130681fd))
- consolidate toast logic into "showToast" ([ba777ed](https://github.com/continuedev/continue/commit/ba777edf4761f8bb781fce11ce45311c0b089048))
- continue sdk ([c7829bb](https://github.com/continuedev/continue/commit/c7829bbd514b962caad99a4c8c660e94f9e577b4))
- convert issue templates to issue form templates ([#507](https://github.com/continuedev/continue/issues/507)) ([fa7f2cb](https://github.com/continuedev/continue/commit/fa7f2cbdeb6013ec4bb081cb85988817f54d070c))
- **core:** add support for Llama models on Bedrock ([#1499](https://github.com/continuedev/continue/issues/1499)) ([53aab1e](https://github.com/continuedev/continue/commit/53aab1e6fd2a0f21fbca1c29b82d1f96d2f5e074))
- crawl `.mdx` docs ([d9f0c4f](https://github.com/continuedev/continue/commit/d9f0c4f131010bd4dae111b5e4290f3460d49e3a))
- create "rebuild index" dialog ([c978d7c](https://github.com/continuedev/continue/commit/c978d7c871be71cd399d02159be8c1b37a0d36ff))
- create `ApplyManager` ([652f8c4](https://github.com/continuedev/continue/commit/652f8c4c7b0879f9ab3b2cc1a25c9530de467c94))
- create docs cache ([3f8afbe](https://github.com/continuedev/continue/commit/3f8afbefeb36c389528d420ef5945c0014ef18c0))
- create file button in toolbar ([268f55b](https://github.com/continuedev/continue/commit/268f55b001e880fec92ef87df6be2bdf8fc25649))
- create markdown rules in notch ([c2571e2](https://github.com/continuedev/continue/commit/c2571e2a86e373199fe9660f95fa5b168c776035))
- create org slice ([71f8508](https://github.com/continuedev/continue/commit/71f85084f22333337a1e817e0502044771d66de4))
- create profiles slice ([9c2db6b](https://github.com/continuedev/continue/commit/9c2db6b9c7d7fd0a0164b3e2288a4135df759707))
- create RecentFilesService ([d597e6f](https://github.com/continuedev/continue/commit/d597e6fe5ea632f2bdf762ad2555c056fd736ba8))
- create stream complete reducer ([ce7d982](https://github.com/continuedev/continue/commit/ce7d98291e30658b387dd5d7de96e0f3577065ab))
- create SVG tooltip ([0cf152e](https://github.com/continuedev/continue/commit/0cf152ede652d975fd774faa40494020c7514196))
- dallin feedback pt 2 ([d158fab](https://github.com/continuedev/continue/commit/d158fab740cf803074106717560941cca8cb96c9))
- dallin's feedback ([e884853](https://github.com/continuedev/continue/commit/e88485348c8b53e9f19d1f9fc0c8b33dd5266df7))
- Dallin's feedback ([fc1ee33](https://github.com/continuedev/continue/commit/fc1ee33ed912543f596d32ae6eaacbbdbc3bcae4))
- dallins feedback ([33b1d39](https://github.com/continuedev/continue/commit/33b1d3994654f311dcae385f6ef2ef78696d1d89))
- dallins feedback ([25b5b8c](https://github.com/continuedev/continue/commit/25b5b8c50b05fe872d74b60ced4a6d8569686ed6))
- dallins feedback ([6070a64](https://github.com/continuedev/continue/commit/6070a645dd9741b433ad43fd09f37b42ec614a3e))
- disable chat-input enter-button during prompting ([e01fbe7](https://github.com/continuedev/continue/commit/e01fbe7f2cf6f53f5a611afe6df53dfc5f710e55))
- disable chat-input-editor during prompting ([7368767](https://github.com/continuedev/continue/commit/73687670da1cc7792d2c4d9ccd7b9813746a4658))
- display rules used ([53f0679](https://github.com/continuedev/continue/commit/53f06795d3910e1ec5aecdbc1fde706f68649614))
- editor change listener ([28d2c7d](https://github.com/continuedev/continue/commit/28d2c7d7e0a00c23c33ad25b8bfc3651ddafa2c5))
- **embeddings:** add gemini provider ([#1362](https://github.com/continuedev/continue/issues/1362)) ([5224572](https://github.com/continuedev/continue/commit/52245724089329c792eb79c8d512cea2f617c4a1))
- enable comment filtering ([471ca9e](https://github.com/continuedev/continue/commit/471ca9eae5e1f82e04b2d6d600631bf24876ec06))
- enable sourcemap ([2be2b73](https://github.com/continuedev/continue/commit/2be2b73f2a28834cbfef9100f479ecffc0ad13c8))
- enable WAL (Write-Ahead Logging) for improved performance and stability ([#1885](https://github.com/continuedev/continue/issues/1885)) ([e93ce84](https://github.com/continuedev/continue/commit/e93ce84cd57eacfe820c1f22668ed069020ee46e))
- enhance help center ([#1755](https://github.com/continuedev/continue/issues/1755)) ([f2a04ef](https://github.com/continuedev/continue/commit/f2a04ef3e9e49876042077f36da5f457630dcaf1))
- Enhance MCP connection refresh logic ([1a0d411](https://github.com/continuedev/continue/commit/1a0d41128f30c7f5c087c7255d6ec3292c288664))
- Ensure CancelAutocompleteAction updates on EDT ([277c20f](https://github.com/continuedev/continue/commit/277c20f3b08eaa1b0b293b06bbfce40404e5477d))
- evaluate rule ([1a1b854](https://github.com/continuedev/continue/commit/1a1b854fda4630e136d0d18b4ea69e71a00ced1e))
- explore btn ([14c9528](https://github.com/continuedev/continue/commit/14c952887399f12e739d6a0fafbdbb4d1355fe90))
- explore dialog ui ([c6cfa93](https://github.com/continuedev/continue/commit/c6cfa9334690cf7f9ecdf99959537ce522728079))
- explore dialog watcher ([741ad35](https://github.com/continuedev/continue/commit/741ad35195d6b841b459076b8dbca1057d5f6358))
- explore dialog watcher ([192a47f](https://github.com/continuedev/continue/commit/192a47f998d639f307933f33a731832ff35305ac))
- explore hub card ([5a777ce](https://github.com/continuedev/continue/commit/5a777ce464243b09d466d7a11a39ff9e45a74ec5))
- extract paths ([2ffb4ae](https://github.com/continuedev/continue/commit/2ffb4aefeb01073c924beb1c203e60c586cf96bb))
- fall back to Cheerio for headless crawling ([fd7bbce](https://github.com/continuedev/continue/commit/fd7bbce79b831a1068c5367bc4f018fe820a917a))
- fallback to chat model from apply model ([46e8dd7](https://github.com/continuedev/continue/commit/46e8dd7c38858173f375ac24f6857ed3bea2b190))
- fallback to chat model in IDE ([b601c22](https://github.com/continuedev/continue/commit/b601c22d01478d74a66bf162f3e12c4c4e1a93d7))
- filttrex ([b20b1ab](https://github.com/continuedev/continue/commit/b20b1ab5ee44d704619327a3b087b1290c634dd7))
- fix address ([d59b3b5](https://github.com/continuedev/continue/commit/d59b3b5297924b2b6343a990c09551be4af9c670))
- fix broken links ([66345e8](https://github.com/continuedev/continue/commit/66345e83550a898cd36eae67a68fb020ce957677))
- fix ctx providers w/ slash cmds ([e5608a9](https://github.com/continuedev/continue/commit/e5608a94e543cd8b66d68c222eacc82e8c021aff))
- fix filepaht issues w/ apply ([6c848ba](https://github.com/continuedev/continue/commit/6c848ba7a7fc7ea32a963653b83b5099e5ed6fad))
- fix models ([d401a5c](https://github.com/continuedev/continue/commit/d401a5c11fdf5b3267269ff3603f83318a9dd9ad))
- fix type error + formatting ([bb5e8b6](https://github.com/continuedev/continue/commit/bb5e8b641dc535fd8d60de92aba04680f7b93824))
- fix version and address ([ad0fdb9](https://github.com/continuedev/continue/commit/ad0fdb988b76bebadcbb26a3069b7f821207680f))
- get paths ([9cb5170](https://github.com/continuedev/continue/commit/9cb5170b1a23552f917265dc330ff1e05838a3de))
- get return_type and parameters from snippets ([bf426d5](https://github.com/continuedev/continue/commit/bf426d5e2147248c06ab51cab31ac5cd9e834b1d))
- glob rules ([8c5ca40](https://github.com/continuedev/continue/commit/8c5ca401d4fbda64eacf23cc0585e2c9830db345))
- go definitions ([bf65b1e](https://github.com/continuedev/continue/commit/bf65b1ebbdd3b0913d141e9f60a11414088bc78f))
- **gui:** add ability to change the session title ([1b32222](https://github.com/continuedev/continue/commit/1b32222c10c6ae6f1fd6c93f6abd8cbeab16f108))
- **gui:** Add Azure as a provider ([b7c0623](https://github.com/continuedev/continue/commit/b7c0623ac6c5067ccefd7c8486eeef2ff56d9667))
- **gui:** add flex-grow to TdDiv in history page ([#619](https://github.com/continuedev/continue/issues/619)) ([7106f42](https://github.com/continuedev/continue/commit/7106f42cb4ba7424b3dda8ed2d0e562e2fd99447))
- **gui:** improve editor highlights ([7bffa2d](https://github.com/continuedev/continue/commit/7bffa2dbeab4dde415c113bb15b71968bc51f448))
- **gui:** more config for azure provider ([e11206b](https://github.com/continuedev/continue/commit/e11206b0fa70075d21d1cc8dd4426af856ee05c2))
- handle deletions ([2c8cee9](https://github.com/continuedev/continue/commit/2c8cee9fd42fc8f18c9e6e37781e27276dae2786))
- hide empty code lines in markdown preview ([#815](https://github.com/continuedev/continue/issues/815)) ([793d022](https://github.com/continuedev/continue/commit/793d022eab292219cda75bb3255ed3d676585977))
- **history:** add sticky headers to history sections ([#621](https://github.com/continuedev/continue/issues/621)) ([3d398d0](https://github.com/continuedev/continue/commit/3d398d0b3781db8bbc51ad00f47e80b3124f5c8f))
- hover brightness on tool call div ([dba2887](https://github.com/continuedev/continue/commit/dba288748d850c7fa8047efb75e954384410a497))
- **httpContextProvider:** load AC on fetch client ([#1150](https://github.com/continuedev/continue/issues/1150)) ([638f192](https://github.com/continuedev/continue/commit/638f1922590d7bd46d6e4d46a4d25bf1b33b26fa))
- if rules ([6224af6](https://github.com/continuedev/continue/commit/6224af63d36e6b96b068c6d4230ee2315bd94d43))
- if rules ([d0e922d](https://github.com/continuedev/continue/commit/d0e922dad4947ac8af4e9d0c76921da56cdc2953))
- if-rule ([211fcb7](https://github.com/continuedev/continue/commit/211fcb77407cd261f2bb26198667388ea6270a05))
- if-rule ([9baa4e9](https://github.com/continuedev/continue/commit/9baa4e93be7f9c2fe057e453237eaa9d1aaa8ff0))
- if-rules ([06b76d7](https://github.com/continuedev/continue/commit/06b76d752fb4f83b14053fffe2f85cb819e39ae6))
- if-rules ([0a13a46](https://github.com/continuedev/continue/commit/0a13a4604d7607793bea66b9cea3e89353bf3af4))
- if-rules ([ccdc1b9](https://github.com/continuedev/continue/commit/ccdc1b953e22ca4aa26e32b9e80bc31ea30f274c))
- ignore node_modules in js and ts code definitions ([3c9071a](https://github.com/continuedev/continue/commit/3c9071a09b114a019a593a7f241975f419cc4e43))
- impl /multifile-edit ([f88f28b](https://github.com/continuedev/continue/commit/f88f28b8551e95e4eb346fafa68d6223f9017d5a))
- improve chat thread ui for better readability ([#1786](https://github.com/continuedev/continue/issues/1786)) ([8478af6](https://github.com/continuedev/continue/commit/8478af63c18b0c35d59d2326ca4e8687a42a624b))
- improve chunking desc on large projects ([3844208](https://github.com/continuedev/continue/commit/3844208a3b9b3daaff085daad8ab933f27fcb5f3))
- improve dropshadow on jb inline edit ([35b94e7](https://github.com/continuedev/continue/commit/35b94e710260782d0311ee22a2849203b8965e38))
- improve dropshadow on jb inline edit ([75e2d2f](https://github.com/continuedev/continue/commit/75e2d2f39df30424b3831615ee3720a2844a60a5))
- improve fatal error message ([1d77ad3](https://github.com/continuedev/continue/commit/1d77ad31949042c7702b59f83a82ca51dcce4900))
- improve input and tooltip ux ([#1923](https://github.com/continuedev/continue/issues/1923)) ([2c13776](https://github.com/continuedev/continue/commit/2c13776c06cfe9c775777bf2fffdcf7dfa2169ee))
- improve model retrieval logic in InlineEditAction ([52b1704](https://github.com/continuedev/continue/commit/52b1704f3c0fe991b32959d04c660fe55e899b2c))
- improve prompt log formatting + detail ([2376daf](https://github.com/continuedev/continue/commit/2376dafce325c87d87090e7cf995beffcb98d3b4))
- Improve ProtocolClient initialization and GUI loading ([0c829fd](https://github.com/continuedev/continue/commit/0c829fd99d0e898c1afdc90a0957c907dcb7f4e2))
- improve settings tabs ([a5f974b](https://github.com/continuedev/continue/commit/a5f974bf1602dabe3939ddc0ac7b5a05ac8e716b))
- improve StreamError dialog ([ecb63a0](https://github.com/continuedev/continue/commit/ecb63a0222c7ae5e051bea02d98a4a2de7c1e2e5))
- improve styling on code to edit ([47fbe4f](https://github.com/continuedev/continue/commit/47fbe4f9b81e1ef31d60f1110d2bf240fa6e7d40))
- include recently + open files in codebase search ([#1833](https://github.com/continuedev/continue/issues/1833)) ([3e0fae3](https://github.com/continuedev/continue/commit/3e0fae35a75bb8dc117bc3eb008eee122e0e12ae))
- init profiles prefs ([d6ad3e2](https://github.com/continuedev/continue/commit/d6ad3e2071ca1ee3f0b5cd4d8e93852a84fd138a))
- insert prompt into input on click ([a73123e](https://github.com/continuedev/continue/commit/a73123e35ce29f1776b5afb456c9c67dc9495099))
- instant apply check for diff rejectection ([98cb185](https://github.com/continuedev/continue/commit/98cb185c926d49a6f5af00f8b53e207743da815a))
- integrate Moonshot AI model provider and update UI translations ([7ffb95e](https://github.com/continuedev/continue/commit/7ffb95e9d0fc964b55b3f92149515fa79fc3aa0d))
- items used text ([#1973](https://github.com/continuedev/continue/issues/1973)) ([28a2042](https://github.com/continuedev/continue/commit/28a2042b7f604889cd0c7c76ee3ec0791a00e3ba))
- **jb:** add ide logs ([22d84ae](https://github.com/continuedev/continue/commit/22d84ae726c40d65449c39b2ece4101f32608870))
- **jb:** add plugin actions ([629641a](https://github.com/continuedev/continue/commit/629641a4adf2a1e2fe1c9a69fd431718b34c4263))
- **jb:** create per-IDE tutorial files ([e3b5cbf](https://github.com/continuedev/continue/commit/e3b5cbfda453c727c6f81d04aa8bed1b9efe8301))
- **jb:** fix meta key bugs in tiptap ([7682c71](https://github.com/continuedev/continue/commit/7682c71575c7ffc03d625510187d792e9d971227))
- **jb:** impl "apply" button ([802cb43](https://github.com/continuedev/continue/commit/802cb43cd6b6abf8613591eb38c445e6bd4daa7d))
- **JB:** impl `showFile` ([24bb5eb](https://github.com/continuedev/continue/commit/24bb5eb0dab8467f606d249f8be16fcb9ff083c0))
- **JB:** scroll to top of file on full file edit ([c36a1ae](https://github.com/continuedev/continue/commit/c36a1ae62851383978080bc5674e7ba4ca0e9cc1))
- jetbrains tutorial explore hub ([054b095](https://github.com/continuedev/continue/commit/054b095dab51b3abc7a3d83587e0b5f67b8acdf6))
- make [@codebase](https://github.com/codebase) a hardcoded ctx provider ([#1818](https://github.com/continuedev/continue/issues/1818)) ([7b86678](https://github.com/continuedev/continue/commit/7b866787d947106b77ac0cef92b75ba36e09e7fc))
- make `assistant` optional on sdk ([48429ae](https://github.com/continuedev/continue/commit/48429ae04f957a98c24d841316c722b91841c329))
- make deletion line highlight wider ([10a5ec1](https://github.com/continuedev/continue/commit/10a5ec18a9df189d58c60886be296f1107a1dfc2))
- make disabled state a tooltip ([#1653](https://github.com/continuedev/continue/issues/1653)) ([6cf0102](https://github.com/continuedev/continue/commit/6cf0102875316a89752d17d4fe08e4b21fd2e603))
- manual ripgrep downloads for JB ([587459c](https://github.com/continuedev/continue/commit/587459c007ba130a4fd6f5ffde246dd8625486a8))
- markdown rules ([a21d350](https://github.com/continuedev/continue/commit/a21d350ea14a98cf16904ae678de64a83775da7c))
- merge conflicts ([82a328c](https://github.com/continuedev/continue/commit/82a328ca15306e0b902075c0661cd1b2bb8ed9a8))
- model name update ([045c6bd](https://github.com/continuedev/continue/commit/045c6bdfec45ffadb25b3db2306b78f34780e84b))
- modify vitest command, add test coverage feature ([96c70d0](https://github.com/continuedev/continue/commit/96c70d07d283130176cbda68afdb02ddc6b20366))
- more cleanup ([6c294e7](https://github.com/continuedev/continue/commit/6c294e76834561848305ac8fb6bcb707ea21b172))
- more cleanup ([bf7cf66](https://github.com/continuedev/continue/commit/bf7cf661cc4357029492f067ca57b1a43401aaaa))
- more dynamic imports for LanceDB ([90c6631](https://github.com/continuedev/continue/commit/90c663146edc965bbe0b87b737755742315a8fa1))
- more moving around ([b0492dd](https://github.com/continuedev/continue/commit/b0492ddfee2e6050d833d4ad07a942dbdd830c55))
- more styling updates ([c7b3ea8](https://github.com/continuedev/continue/commit/c7b3ea890ac2bade08424e7a23301cea319aea35))
- more visible assistant refresh + submenus ([1fd2ce9](https://github.com/continuedev/continue/commit/1fd2ce90f22c0dd6ebb9ec5b2542f85475844436))
- move .prompts into slash cmd ([1d1f705](https://github.com/continuedev/continue/commit/1d1f705f7dfb1d517b90e03b82856e625f3f323e))
- move `StepContainerPreToolbar` ([979bcb9](https://github.com/continuedev/continue/commit/979bcb97ec88d91d0ed0bd14fd5b6b7dc4c670ae))
- move apply accept/reject into lump ([071ecb9](https://github.com/continuedev/continue/commit/071ecb91eb9c627e96439b7a5be260ec446500b0))
- move apply manager instantiation ([2413d24](https://github.com/continuedev/continue/commit/2413d241c6448f65c99d9286e72a01bc8acc429a))
- move Edit into Chat page ([731b54e](https://github.com/continuedev/continue/commit/731b54eb30bd7184e42c397db658020b4a70d5f7))
- move error indicator into lump ([1f1d4f4](https://github.com/continuedev/continue/commit/1f1d4f4a1c5b365258db219e2a0ae8eecf3656a8))
- move free trial out of assistant ([2c58a33](https://github.com/continuedev/continue/commit/2c58a33a6a937ea7beb1aa017bce64ec4cfe32d0))
- move rule parsing to `config-yaml` ([f2d7290](https://github.com/continuedev/continue/commit/f2d72902cb45c165ca73483dd5c0a90ac89bcc49))
- move SymbolLink ([e3bbd6b](https://github.com/continuedev/continue/commit/e3bbd6b7cfa04183f7a60470a55e4a2618083a21))
- move vLLM rerank response in the VLLM.ts and remove unused types ([c8d9d95](https://github.com/continuedev/continue/commit/c8d9d959f10b34cb0af857cfa178c1f9df30f5cf))
- onboarding card upgrade tab ([41bc0e9](https://github.com/continuedev/continue/commit/41bc0e922b333ee404ddb17e5bdcf7c83b7a6ca0))
- only autoscroll Continue console when focused on last element ([40390f5](https://github.com/continuedev/continue/commit/40390f583bea74766c3eff48cb8ceda555112780))
- open lump on submit onboarding ([f77f662](https://github.com/continuedev/continue/commit/f77f662c6e7313db645656e003c9d0145646388e))
- org select ([dc751a3](https://github.com/continuedev/continue/commit/dc751a3274948cb12b47fb7b57106a8349e26870))
- php definitions ([8da703f](https://github.com/continuedev/continue/commit/8da703f74525213dc212efa6b763d4be06dee38b))
- php definitions ([eadc96e](https://github.com/continuedev/continue/commit/eadc96ebef6653a996dff32095fd87a86703058d))
- poll in JB after upgrading ([89d4b32](https://github.com/continuedev/continue/commit/89d4b32bba3bede3ac9f001c6c013ec0bb5a9ca0))
- postgres context provider first slice ([758a81c](https://github.com/continuedev/continue/commit/758a81c7373f398294ccb80d00a5c7d181f151d7))
- pr_checks update ([1cc81d4](https://github.com/continuedev/continue/commit/1cc81d4b7e6ed6aedcc19753eea623acbfc8f8c1))
- preserve edit when rejecting ([32bf80d](https://github.com/continuedev/continue/commit/32bf80d7d280fe815b547475b4a75fce92ba990d))
- profiles slice ([b82d29c](https://github.com/continuedev/continue/commit/b82d29c2df154a632c89d32c6847f5bf43b6564b))
- promote reject diff action ([16cbe94](https://github.com/continuedev/continue/commit/16cbe9499431d7eec64d0695821a223302d980cb))
- prompt blocks ([9df325a](https://github.com/continuedev/continue/commit/9df325a71f3340c46820ac5335249095cda443ac))
- Provide workspace path to HttpContextProvider ([4873f58](https://github.com/continuedev/continue/commit/4873f5805f89bc1f443211515dd7afb3fbc2cfff))
- python context ([42d5f66](https://github.com/continuedev/continue/commit/42d5f660727dcf315d12193f70d23b283e7af10d))
- recursively apply quick actions codelens ([#1925](https://github.com/continuedev/continue/issues/1925)) ([d5155da](https://github.com/continuedev/continue/commit/d5155dac697e5fa8b4241ebebe48bbfa66dc2c55))
- redo action ([121be85](https://github.com/continuedev/continue/commit/121be85a69837a1d31dde31f7fca7098458ed38d))
- refactor onboarding card ([b86bc6f](https://github.com/continuedev/continue/commit/b86bc6f727e292c2d3a8fb1511646c36e2a2531f))
- reintroduce lazy apply for full files ([6504bd5](https://github.com/continuedev/continue/commit/6504bd55659823c8117d04ddb447f91d9932d086))
- remove `models` property ([7f8882e](https://github.com/continuedev/continue/commit/7f8882eee136b90db9400175f9ee00fa1aa3ad93))
- remove defaultTitle ([1bf359a](https://github.com/continuedev/continue/commit/1bf359a55fdb833569878167a1440e66c65d7249))
- remove edit as a mode ([4f371a0](https://github.com/continuedev/continue/commit/4f371a0eb654067c626a41eb53718f565873975b))
- remove tools from schema ([5bbda4f](https://github.com/continuedev/continue/commit/5bbda4f115a208ebd2df0c63ede75bfc5e131b54))
- remove useFileExists ([2b34ee8](https://github.com/continuedev/continue/commit/2b34ee8c4156c544a3abd9616080f4d8e0b565a1))
- rename to `Open Assistant configuration` ([9d8139a](https://github.com/continuedev/continue/commit/9d8139a6fae992eb1cb77248cc297d0a95683c12))
- replace logo and deepseek model base info ([7749349](https://github.com/continuedev/continue/commit/7749349f8996f5da186ba1a8b2910443121ea2fc))
- restructure for easier module publishing ([b51905d](https://github.com/continuedev/continue/commit/b51905da95307ed17f3fa7a466e94514bcd4113a))
- retrieve AWS credentials from Env and from ECS/EC2 instance ([53426a1](https://github.com/continuedev/continue/commit/53426a12b858f341c9f70f8ee3513dbea39d648d))
- reusable card ([d11376b](https://github.com/continuedev/continue/commit/d11376ba7f2b7ab1a85c75f361631fc2c5c26ab5))
- rule colocation ([39dc367](https://github.com/continuedev/continue/commit/39dc367371c0df605f663c76fe490c4736f00153))
- rule glob ([f95cdfa](https://github.com/continuedev/continue/commit/f95cdfaf83569159cd7aed02134be7dd5f4029cf))
- rules ([2620064](https://github.com/continuedev/continue/commit/2620064c53ecf7de3d61b34043d2c0450f68b13c))
- rules display ([40914a8](https://github.com/continuedev/continue/commit/40914a8bf3b4f2c1764873bc3172feab3bf23f3a))
- rules policies ([8a2ffb3](https://github.com/continuedev/continue/commit/8a2ffb3a859d4fbec3299500a0d860c0f725573d))
- rules preview ([d57d35d](https://github.com/continuedev/continue/commit/d57d35d5bdf6015593283a2aecbf88bca1c8c574))
- run prettier ([ecdb4f5](https://github.com/continuedev/continue/commit/ecdb4f530dee01e8a67e7fd964206428d379b4db))
- **scaleway:** update supported models ([8b04ba6](https://github.com/continuedev/continue/commit/8b04ba6b233d72f1e61ea67fd63de51d03629282))
- separate toolbar action buttons ([aba6e4e](https://github.com/continuedev/continue/commit/aba6e4e61c6c7b548126a79ffeb0608e1de1147b))
- show disableIndexing in More ([5d95b62](https://github.com/continuedev/continue/commit/5d95b62e683c0ea50eb519f7cc2851736e8ee64b))
- show num diffs in toolbar ([ad6c0d0](https://github.com/continuedev/continue/commit/ad6c0d0bb850308993e53c4117cc45664fd8970e))
- simplify types ([b18fd4a](https://github.com/continuedev/continue/commit/b18fd4aee0c66e41113bce4de1fa526da1dc16c6))
- simplify typings ([c6fa6d7](https://github.com/continuedev/continue/commit/c6fa6d78a714ba6bba44ec2ef9825080b0a5b69a))
- single default quick pick to edit ([#1743](https://github.com/continuedev/continue/issues/1743)) ([ca7bde9](https://github.com/continuedev/continue/commit/ca7bde9b5e10d684ea44291c67eb294edc357240))
- skip hub onboarding in free trial ([d995149](https://github.com/continuedev/continue/commit/d99514995588cc2c502df99757e22f5ac3e2c7a1))
- skip onboarding subtext ([9e6ff4b](https://github.com/continuedev/continue/commit/9e6ff4b91447b03d1b9949499cf5135d7a369df9))
- smaller headings, use assistant name ([85804ff](https://github.com/continuedev/continue/commit/85804ff9e54adf30f1a4b23b3003b39338a82065))
- split diff ([b7defc8](https://github.com/continuedev/continue/commit/b7defc8d912b09d9323e05a82442bea17c6faef4))
- split diffs ([3c537f5](https://github.com/continuedev/continue/commit/3c537f51c13ee9064a3e17f3aec62dc5283ae3e2))
- split diffs ([39087c2](https://github.com/continuedev/continue/commit/39087c2207e0dd3764761e91b9a2abd3ca6db906))
- support o3/o4 as agents ([8bb18fd](https://github.com/continuedev/continue/commit/8bb18fd2ee4a0fb81b2f24903e47fb1b0beade36))
- supports agent 4 deepseek ([82d8e1e](https://github.com/continuedev/continue/commit/82d8e1e6795e7258880d414c4bb78c6f83b17883))
- toolbar header for all codeblocks ([635f7cb](https://github.com/continuedev/continue/commit/635f7cb168f264446e40c6887347327b101ee413))
- tutorial listener ([6e01c7d](https://github.com/continuedev/continue/commit/6e01c7d0ca0d139e471edf40018d35e1033638f3))
- unskip tests ([a5619e2](https://github.com/continuedev/continue/commit/a5619e29c8a0230f578c092c028c7cc6f3066afb))
- update azure uri for foundry users ([638969b](https://github.com/continuedev/continue/commit/638969b7c7594f36bfefd59a986032ba597f88fa))
- update btn colors ([b7c7171](https://github.com/continuedev/continue/commit/b7c71719e55e728f944399789f6749e493d5d13e))
- update docs and input labelling ([1847317](https://github.com/continuedev/continue/commit/18473174365c88e7e83fd8123cb6af9318e10462))
- update e2e tests ([93cec27](https://github.com/continuedev/continue/commit/93cec27e28773778e3223424fb72026c9e3ead44))
- update executable perms on linux/macos ([dcbd80e](https://github.com/continuedev/continue/commit/dcbd80ecf47adeb49b9484bb1e1b3c43ff80bff8))
- update onboarding w/ embeddings model ([#1570](https://github.com/continuedev/continue/issues/1570)) ([ed56c8f](https://github.com/continuedev/continue/commit/ed56c8f7f325f19c9b4d1c7e3cb775f850beaea9))
- update PreToolbar ([3933791](https://github.com/continuedev/continue/commit/39337910f125a71414cf2c39b96250fc512777be))
- update redux store schemas ([438cba4](https://github.com/continuedev/continue/commit/438cba4501026ef1705987b23f419570d1175ea1))
- update styling ([ef349ec](https://github.com/continuedev/continue/commit/ef349ecec90dac3d15eb80ae17cf24c155d1e683))
- update sys prompt ([25c7144](https://github.com/continuedev/continue/commit/25c714494588433d1c43d6af99cce175b48e21cc))
- update to work as normal context provider ([0ace169](https://github.com/continuedev/continue/commit/0ace1699356be8024a8ab49ccadc4b1b22c18e08))
- update tutorial files w/ agent mode step ([8eaf078](https://github.com/continuedev/continue/commit/8eaf078638c0d2d9c14aa0cc8e465df66f01e4df))
- update URL replace logic ([fe6c9a1](https://github.com/continuedev/continue/commit/fe6c9a1477ab814f3cc4d0973e851701621d2c54))
- updated prompt docs ([204aa51](https://github.com/continuedev/continue/commit/204aa51a944906fe629e92095a3f7f86113b9f6c))
- use @lancedb/vectordb-win32-arm64-msvc ([0277320](https://github.com/continuedev/continue/commit/027732073aa0fb5202b558f4c0d71c71c6b8acbb))
- use `fetch` instead of `http` ([401d67b](https://github.com/continuedev/continue/commit/401d67bbd0dc11fd6eda2f7d9c17dfd4da0a43f5))
- use `instant` property on diff manager ([2f12bbd](https://github.com/continuedev/continue/commit/2f12bbdf25b1ff189a88d37bfba4f3bfe7f2f3bc))
- use bm25 for fts ([2e5b579](https://github.com/continuedev/continue/commit/2e5b5794e9b394405c127893100be21e008b45ad))
- use clipboard content ([9dd6284](https://github.com/continuedev/continue/commit/9dd6284b8e4b1dc09b704bc00d5231af9569b6bc))
- use correct deployment for azure ([8e24fcf](https://github.com/continuedev/continue/commit/8e24fcf4a0ee655dfbe83d95b82855041f0ba246))
- use crawlee for docs service ([a51c520](https://github.com/continuedev/continue/commit/a51c520deefa49f9d67aaa2469bb261af00dab80))
- use exponential backoff in llm chat ([#1115](https://github.com/continuedev/continue/issues/1115)) ([a87df40](https://github.com/continuedev/continue/commit/a87df40a2c875a1538aa112d7d35c03137990ee2))
- use git diff, improve comment formatting ([1adbd9a](https://github.com/continuedev/continue/commit/1adbd9a456f6c4c2f56b78d6bd5e3b8737f60ec5))
- use hub blocks for local onboarding ([1b45308](https://github.com/continuedev/continue/commit/1b453083b14e60afda14de69b7bfb2f7ce74194b))
- use meyers diff after initial edit/apply ([f5e7f9c](https://github.com/continuedev/continue/commit/f5e7f9ce71d4c34e8ee72b0d78fe89c68d0b7170))
- use s3 for global docs cache on all docs ([0156409](https://github.com/continuedev/continue/commit/0156409dac5a445b055ede921c1c07706759ff4e))
- use theme color for shortcuts rows ([44fdd65](https://github.com/continuedev/continue/commit/44fdd65967b561527865a02cafce0de4cdc9fa70))
- use thunj ([eae1dd2](https://github.com/continuedev/continue/commit/eae1dd20bb296099db94175c43bf7af445e97200))
- v1 onboarding card ([0d47647](https://github.com/continuedev/continue/commit/0d476473bda2ef8bea78064c89c5bc1f6818fec1))
- vitest ([3cd19f5](https://github.com/continuedev/continue/commit/3cd19f5fabdb6eab3be381ccef8de743b37fcf29))
- **VSC:** give option to disable quick fix ([951784c](https://github.com/continuedev/continue/commit/951784c790664a80d87b54e33886a3582137ad50))
- vscode config for json ([666fb18](https://github.com/continuedev/continue/commit/666fb186d6b4cee9dcc95e1adc6b42748300de4c))
- withExponentialBackoff utility function ([ad8991e](https://github.com/continuedev/continue/commit/ad8991eac0f7f40f3877d56392592d8d83e173a6))
- working `copy-client` ([09beb58](https://github.com/continuedev/continue/commit/09beb58260f32043c89cff39c6e8567f25099c9c))
- write initial unit test for apply ([f4a59dc](https://github.com/continuedev/continue/commit/f4a59dc185c3ea7ceb29a4629ea0c7dc82f44dbb))

### Performance Improvements

- :green_heart: hardcode distro paths ([edf0f56](https://github.com/continuedev/continue/commit/edf0f5603730d01d7ca4ca055dd49da596648627))
- :zap: don't show server loading immediately ([5047dfc](https://github.com/continuedev/continue/commit/5047dfcd2a2e47468c15ed05c69781cc615ef723))
- **llm:** Optimize pruneLines functions in countTokens ([35b3189](https://github.com/continuedev/continue/commit/35b3189538da5891617a79ba9c0badb0bb7dc1bc))
- **llm:** Optimize pruneLines functions in countTokens ([28cdd1c](https://github.com/continuedev/continue/commit/28cdd1cee25973aa9a8a5cfdcbcc436e0b7f6240))
- **llm:** Optimize pruneLines functions in countTokens ([881f8b3](https://github.com/continuedev/continue/commit/881f8b3139794aca0a6699c3d34ebf8fba01b789))

### Reverts

- Revert "Add citations to the log" ([caf7288](https://github.com/continuedev/continue/commit/caf7288d36c1361b6cb279821d9ee377636f72f7))
- Revert "feat: add bookmark logic" ([730ac56](https://github.com/continuedev/continue/commit/730ac56d5a15a6b2931330aeb849e2d260d2644a))
- Revert "update TabBar to use Redux for session management" ([9f13b92](https://github.com/continuedev/continue/commit/9f13b92b3fbc4157124ccf120028312390eed70f))
- :bookmark: update version ([d6ebc6d](https://github.com/continuedev/continue/commit/d6ebc6d969ccafc753ecf00e0309777b96b4ad11))
- :bug: revert unecessary changes from yesterday ([3629ddd](https://github.com/continuedev/continue/commit/3629dddb1daea23fbd29b03705e742ec2a22d6ec))
- :fire: disable fallback_context_item ([a572db4](https://github.com/continuedev/continue/commit/a572db40b6c9ce98b07e89d34f8652e19f91187e))

# 1.0.0 (2025-06-22)

### Bug Fixes

- :adhesive_bandage: allow GGML to use api.openai.com ([db19f6b](https://github.com/continuedev/continue/commit/db19f6bc98285d8ea45b4db16f619dffbec7c3db))
- :adhesive_bandage: skip indexing really large files ([b773fe6](https://github.com/continuedev/continue/commit/b773fe6d7a0b489a658139ea5fc958abd46a20b2))
- :ambulance: catch error from meilisearch client.health ([00775f5](https://github.com/continuedev/continue/commit/00775f54e6c3fa8044a996ea1a7cf0f2205735dd))
- :ambulance: class_name hotfix ([83b0417](https://github.com/continuedev/continue/commit/83b0417f6c8c579d0ea5a0f689eceb822fe7a04d))
- :ambulance: fix import of run from **main** ([ebfe428](https://github.com/continuedev/continue/commit/ebfe428b7f70de66bc5692cca1db7cd10ef4b997))
- :ambulance: hotfix and package.json seo experiment ([42024ef](https://github.com/continuedev/continue/commit/42024effba73673b4080c25806c21293b5daad3e))
- :ambulance: load global ~/.continue/assistants ([4b55cfb](https://github.com/continuedev/continue/commit/4b55cfb2e8ad4803855020111d0cff6a38ad79d5))
- :ambulance: logging to file causing problems with starting server ([8b95ef7](https://github.com/continuedev/continue/commit/8b95ef7de258de8498b328d9e6107a95f57f8d2c))
- :ambulance: specify packagePath for vsix ([512ccfd](https://github.com/continuedev/continue/commit/512ccfda670abb6132e9cd720280a472e53e3326))
- :arrow_up: upgrade openai python package ([19cbe2c](https://github.com/continuedev/continue/commit/19cbe2cebae8e2155b6b4375c6a96a3b25e87615))
- :art: many small improvements ([28f5d7b](https://github.com/continuedev/continue/commit/28f5d7bedab05a8b061e4e7ee9055a5403786bbc))
- :bookmark: update extension version ([05b9642](https://github.com/continuedev/continue/commit/05b96420bda2da0c725cacc8141d87449eaf9e9c))
- :bookmark: update version ([afae160](https://github.com/continuedev/continue/commit/afae1600255714d0a4f18f892d3e7b5e1d921962))
- :bookmark: update version ([9aee2cc](https://github.com/continuedev/continue/commit/9aee2cc44c461ce0e001185af85352e78522bab5))
- :bookmark: update version to try again ([1905f31](https://github.com/continuedev/continue/commit/1905f319470c02ee414498b9101b6e64b4b15d65))
- :bookmark: v3 -> v4 of upload-artifact ([d24862a](https://github.com/continuedev/continue/commit/d24862a6fd22e4eac1b2ca27ce7bf029f0d8fa4a))
- :bug: a few minor fixes ([c918bb3](https://github.com/continuedev/continue/commit/c918bb3af5ec4a4a409eb0a3add27951b00c3c59))
- :bug: a handful of bug fixes ([e1325c0](https://github.com/continuedev/continue/commit/e1325c0153becb95b454810d9461efd7d3624a6a))
- :bug: a number of small fixes + disable summaries ([a975560](https://github.com/continuedev/continue/commit/a9755603c3a2c0b3afe809f77a63824c77c6419e))
- :bug: access highlighted_code through context_manager ([1afb37b](https://github.com/continuedev/continue/commit/1afb37b5bb901d95c493039591b9243cd2cdd6f7))
- :bug: add data file for ca_bundle ([b82d83f](https://github.com/continuedev/continue/commit/b82d83f79389897ed5f05eb9b5e8daf9cf64ee6f))
- :bug: Add requestOptions to YAML config for mcp ([81c20c1](https://github.com/continuedev/continue/commit/81c20c11dbbce2eccefd00364c5b74b298b2f24f))
- :bug: add server/exe to .vscodeignore insteading of manually removing ([e8ebff1](https://github.com/continuedev/continue/commit/e8ebff1e6b07dfaafff81ee7013bb019cbfe2075))
- :bug: additional fixes to ssh /edit ([4428acd](https://github.com/continuedev/continue/commit/4428acdd6f372c3724a908fafb1c793e0eae4096))
- :bug: allow end/home keys to work ([615d30e](https://github.com/continuedev/continue/commit/615d30e3dce92a9993b0e93b044faadf228529b1))
- :bug: allow None for timeout ([ff3de11](https://github.com/continuedev/continue/commit/ff3de1184737f1124090d384b877a30550b60869))
- :bug: another hotfix - don't destructure selectors ([534304a](https://github.com/continuedev/continue/commit/534304a2a4f9abfc221a961f279d1b43d14b6d33))
- :bug: another windows fix in typegen.js ([f38c8fb](https://github.com/continuedev/continue/commit/f38c8fb8b33a705ed4eb4d2e0974060ebb88afd3))
- :bug: async with Client (meilisearch) ([9a0cd64](https://github.com/continuedev/continue/commit/9a0cd644dcb5ff46817a6ea686a6de0fb764c960))
- :bug: attempting to fix mkdir ([c1a8097](https://github.com/continuedev/continue/commit/c1a8097f0a7f3cddb0aebac26e6197ffef186972))
- :bug: automigrate between short/long imports ([eecc2b5](https://github.com/continuedev/continue/commit/eecc2b57c5c5a144abfc0623102438e902c4aeba))
- :bug: avoid removing disallowed file windows ([19a3266](https://github.com/continuedev/continue/commit/19a3266b6f14186bd0839fac8b2a04b5a29f32e7))
- :bug: bug when highlighting code prior to context_manager creation ([74a52c8](https://github.com/continuedev/continue/commit/74a52c8399b3ccf2d2100b088b79e65c6ca6ad7e))
- :bug: bug where old server doesn't get updated ([bb776a0](https://github.com/continuedev/continue/commit/bb776a03df3e6a39a1726b781ea33c2ccebd5343))
- :bug: catch error when workspace uri isn't defined ([fc9eb30](https://github.com/continuedev/continue/commit/fc9eb3051fd5a7c9cad57b5d6cd93374bd8210fb))
- :bug: change for/backwardslash decoding scheme ([a3a05fe](https://github.com/continuedev/continue/commit/a3a05fee312ad7c04d2abb0e186da55c7d061462))
- :bug: chmod for linux as well as mac ([089def0](https://github.com/continuedev/continue/commit/089def08c58120f78df78c10027639802ad8f77d))
- :bug: clear all other selector destrucuring ([145642f](https://github.com/continuedev/continue/commit/145642f1eaf01d5809dabd79e7f64f234124683e))
- :bug: Codebase Indexing was not starting on load ([e391d58](https://github.com/continuedev/continue/commit/e391d583041c54edb3fa0836eb9186c61e6b063d))
- :bug: compatibility with python 3.8 ([275ad6f](https://github.com/continuedev/continue/commit/275ad6f72dafdfacffd9c9b5cc4847135a30f425))
- :bug: convert to correct path sep in wsl URIs ([1b2341a](https://github.com/continuedev/continue/commit/1b2341a0113fadf8c8d23097ef1041d3e3088e84))
- :bug: correct path sep for ssh-remote files ([b9bd8c1](https://github.com/continuedev/continue/commit/b9bd8c1848eaf38d5d15694a1ecae67f14566214))
- :bug: correction to ContinueConfig serialization model ([b8aba4b](https://github.com/continuedev/continue/commit/b8aba4bc96d3b064012a40d837d5191cae20037e))
- :bug: correctly generate uris for remote ([ab31cb1](https://github.com/continuedev/continue/commit/ab31cb15fae74592f49c2ceadc8d7810228fa7e2))
- :bug: ctrl+c for windows overriding copy ([c3925c0](https://github.com/continuedev/continue/commit/c3925c04d981d2abc1e21cf72d6e77d165420a73))
- :bug: custom escaping instead of URI for diff paths ([da3970e](https://github.com/continuedev/continue/commit/da3970e00061b7a223d23f51bd53012666d324dc))
- :bug: default to counting chars if tiktoken blocked ([7006dbb](https://github.com/continuedev/continue/commit/7006dbb3e38a837a2580a516791874f6815ac25f))
- :bug: don't fail on disconnected websocket ([0876610](https://github.com/continuedev/continue/commit/08766100cdb3638b3300ae4b700f8ec2af6b9a8a))
- :bug: don't log stdout to console ([ee4701d](https://github.com/continuedev/continue/commit/ee4701dc45cd540728302ca8a09e9b7ce842597f))
- :bug: don't open continue automatically ([8b76f51](https://github.com/continuedev/continue/commit/8b76f518313c20f13dda605931c9929ef58a7a22))
- :bug: don't override context length param in OpenAI ([b2a6d07](https://github.com/continuedev/continue/commit/b2a6d07ea99be1f9288ee21477edc0874e780cad))
- :bug: ebusy and logging bug fixes ([3d61469](https://github.com/continuedev/continue/commit/3d614690cd825ac5580074ecdc22f660455204f1))
- :bug: fix "code" keyerror prior to context_manager.start ([866b16c](https://github.com/continuedev/continue/commit/866b16c3a9c9d88a7b90aa8a43610fc4884ab123))
- :bug: fix /edit in ssh, pinyin input in combobox ([cda1be4](https://github.com/continuedev/continue/commit/cda1be46625abd8f44962cceeded04c8c47d9f65))
- :bug: fix >c_d.png file path ([a9bc4e2](https://github.com/continuedev/continue/commit/a9bc4e26263faef8598dd8aa2aec7949c75ab70c))
- :bug: fix 2 model config bugs ([b144d21](https://github.com/continuedev/continue/commit/b144d21b48a94aa8c203469eb7667bd22fc4e243))
- :bug: fix 404 from undefined gif ([b467371](https://github.com/continuedev/continue/commit/b4673712e1a6a5b435125004a9b51498207fb7b6))
- :bug: fix automigration ([ec41f55](https://github.com/continuedev/continue/commit/ec41f553c24d5f4b5bc4e601c989b1936d67ae1a))
- :bug: fix azure openai bug for 07 version ([a8e69a0](https://github.com/continuedev/continue/commit/a8e69a02e6897689a1727fb7542ed5684b1348e2))
- :bug: fix broken docs link ([210a02e](https://github.com/continuedev/continue/commit/210a02ef02341a98b4ed18095b2d656a7b994bd9))
- :bug: fix bugs when selecting code to edit ([fa34214](https://github.com/continuedev/continue/commit/fa34214012d14385d231a1ac4f16006aaf4331fb))
- :bug: fix ci to only upload from linux x64, not alpine ([4e1a5b1](https://github.com/continuedev/continue/commit/4e1a5b1fb3f96edc95b0938265da980e98566d56))
- :bug: fix cmd+m bug ([38e8272](https://github.com/continuedev/continue/commit/38e827243ceff3732cd0f260e7a3bd4941a96bc5))
- :bug: fix command enter, stop streaming on reject ([8e15ec3](https://github.com/continuedev/continue/commit/8e15ec3c2c1490d4a7d6371f877368376fd64e8a))
- :bug: fix command enter, stop streaming on reject ([19b3886](https://github.com/continuedev/continue/commit/19b38863c21656526e0729776682430e0fa277da))
- :bug: fix config.py import paths ([97861bf](https://github.com/continuedev/continue/commit/97861bf4117bbc36f8f87797a9ca60e6336f82cc))
- :bug: fix context length bug for /edit ([d103263](https://github.com/continuedev/continue/commit/d103263030ad52debe73bd131c71bbf17f545956))
- :bug: fix dialog links ([4c84e69](https://github.com/continuedev/continue/commit/4c84e6945a7c2018622eceb54e7fb54de193b03a))
- :bug: fix for --meilisearch-url flag ([e2798c5](https://github.com/continuedev/continue/commit/e2798c5bb62eeb2a3bc8f5baee18f9d64ee86563))
- :bug: fix for Azure OpenAI model names ([bcec2a0](https://github.com/continuedev/continue/commit/bcec2a0870d0ef649961b6c91ec866b612680b9e))
- :bug: fix for edit=None in highlightedCode update ([247d3e9](https://github.com/continuedev/continue/commit/247d3e9a41ff8d9fe2da6386bfb0d0eb063b071c))
- :bug: fix for lmstudio defaults ([0012922](https://github.com/continuedev/continue/commit/00129229cd881d6b910a4b01db68e702cdd63a40))
- :bug: fix for windows drive difference bug ([d69c6d4](https://github.com/continuedev/continue/commit/d69c6d4f3729374ab40fcebc861e67f2da100ad9))
- :bug: fix ggml bug ([1a75475](https://github.com/continuedev/continue/commit/1a75475c681053494984664ef1179171fe2a5d83))
- :bug: fix headers for openai.;y ([44fe0c9](https://github.com/continuedev/continue/commit/44fe0c94a55a753ff5d6c3da6b63db4a5c70d780))
- :bug: fix height bug after cmd+shift+R ([a7cb092](https://github.com/continuedev/continue/commit/a7cb0929bd064f73a1e3e49ba8dd6b6b7de387f4))
- :bug: fix history.timeline indexing bug ([01ed2c7](https://github.com/continuedev/continue/commit/01ed2c7eb2d3417b2c190eea105008372f49a7c6))
- :bug: fix huggingface tgi ([5316180](https://github.com/continuedev/continue/commit/5316180394d48d9877cda0cb3d7c3c6de9995d12))
- :bug: fix import in run.py ([4cf1f75](https://github.com/continuedev/continue/commit/4cf1f75518053f9df174d5ab90c426124f85ecfa))
- :bug: fix inability to copy/paste when ipynb is open ([850c8ae](https://github.com/continuedev/continue/commit/850c8aea7f3d9c46ff8e98bde936b92282376dae))
- :bug: fix incorrect imports in default config file ([374bdd0](https://github.com/continuedev/continue/commit/374bdd037792825bf984026da12d4100ffebcac2))
- :bug: fix keyboard shortcut for debugging ([c021958](https://github.com/continuedev/continue/commit/c021958ae893a9683352ba99e5c6301e38331492))
- :bug: fix meilisearch empty body content-type bug ([598e243](https://github.com/continuedev/continue/commit/598e243fd292dd8851865ab1c3915ca55f4992cc))
- :bug: fix missing path import ([5bfe68e](https://github.com/continuedev/continue/commit/5bfe68ea7f7e90e3cb1c3101360cf959b336a857))
- :bug: fix model changing bug ([fd4a4dc](https://github.com/continuedev/continue/commit/fd4a4dcf004bea86d982ffffb66b4e3cb38193a6))
- :bug: fix overriding of system message ([8444e76](https://github.com/continuedev/continue/commit/8444e76b7232fbddb62d3626de13653ae332d168))
- :bug: fix paths ([b893c95](https://github.com/continuedev/continue/commit/b893c956fe75a9e45f06129290d043737f5c1007))
- :bug: fix reducers for user input queue ([1a36a3c](https://github.com/continuedev/continue/commit/1a36a3c02acaf6bf29d4153c113217517b832942))
- :bug: fix replicate to work with models requiring prompt input ([84ec574](https://github.com/continuedev/continue/commit/84ec574e182ec441e95d13c3543a934e0a036228))
- :bug: fix serialization bug for context_providers ([2799249](https://github.com/continuedev/continue/commit/27992499af977baeb9124d9ab35ffec6d36a298a))
- :bug: fix set_system_message ([084fdac](https://github.com/continuedev/continue/commit/084fdac3992f58dcf11241e7e5c2d5efa784ce0d))
- :bug: fix ssh /edit by checking for file through vscode fs ([417d45c](https://github.com/continuedev/continue/commit/417d45ccddc2f434d7467e4f17113783996653dd))
- :bug: fix telemetry bug ([042bc5a](https://github.com/continuedev/continue/commit/042bc5ac76800ee66e603ef23b2bb857fafe053e))
- :bug: Fix the generating animation ([2f402b4](https://github.com/continuedev/continue/commit/2f402b4a1227ade5a4ba70f770974627b586e930))
- :bug: fix timeout type ([e1a0290](https://github.com/continuedev/continue/commit/e1a0290d5a699e30464f1e682cb11c6aa119bd59))
- :bug: fix togetherAI model json parsing ([deb291c](https://github.com/continuedev/continue/commit/deb291c1b225425cba543dd3b4c5557089abfb59))
- :bug: fix undefined.filter bug ([9b58278](https://github.com/continuedev/continue/commit/9b582781ab0aceaaf1cff7432fed92fa6c205aae))
- :bug: fix usages of LLM.complete ([f057ee4](https://github.com/continuedev/continue/commit/f057ee4d619b834dc245065d13417a86b44dc61b))
- :bug: fix when multiple cursor ranges are selected ([f9c145c](https://github.com/continuedev/continue/commit/f9c145c9667e0cd9adb7f9b645f7abf12f7cf2a2))
- :bug: fix yaml syntax error ([11c7cec](https://github.com/continuedev/continue/commit/11c7cecc107ef9f2571926055dbd80495fe0f8b2))
- :bug: fixes for a few context_providers ([41b3233](https://github.com/continuedev/continue/commit/41b3233693c34cd81c872a1e7279721b5f640d60))
- :bug: fixes to templating messages ([c56e24d](https://github.com/continuedev/continue/commit/c56e24d2a5f2b40702e4b495fa3f28d554eaa3ab))
- :bug: fixing bugs with ggml ([87409c3](https://github.com/continuedev/continue/commit/87409c31832ccb707abbf134843323c9eb6e1183))
- :bug: fixing issues with creating markdown files ([5c1c2d6](https://github.com/continuedev/continue/commit/5c1c2d626ffed786d00c79aadef26fa5718ca43d))
- :bug: fixing small UI details ([088b7b8](https://github.com/continuedev/continue/commit/088b7b803866817aaedce6b61834f1ce5de7a7c2))
- :bug: force kill old server with taskkill on windows ([b1b7d13](https://github.com/continuedev/continue/commit/b1b7d13dbf5b9f6ada28a5ef22ea6857d3b0bcb6))
- :bug: ftc fix ([f5f10ef](https://github.com/continuedev/continue/commit/f5f10efee3402e117c34b6f0de4bf2fd7d2819c1))
- :bug: gpt-4-32k in CHAT_MODELS ([b0445cd](https://github.com/continuedev/continue/commit/b0445cd5fc4538c8a9c4f3e76be0f3d724c99818))
- :bug: handle when vscode workspace not open ([73c6827](https://github.com/continuedev/continue/commit/73c6827d02ff62313184e3745fd94c7591c98b61))
- :bug: hotfix for user_input_queue.map ([610c576](https://github.com/continuedev/continue/commit/610c576cc9df72716c5e65838f805b15431011ea))
- :bug: install python-virtualenv on linux, fix git hash files error ([6f0e634](https://github.com/continuedev/continue/commit/6f0e6340bb22ee150ef4b7996750f4c63c0bc2a7))
- :bug: install python-virtualenv on linux, fix git hash files error ([7fa98ff](https://github.com/continuedev/continue/commit/7fa98ffe843320ddc63794a497a2d44570e005c3))
- :bug: kill server before trying to delete exe on windows ([286fb0e](https://github.com/continuedev/continue/commit/286fb0e20e48859f129ccf568d03248805bcbc61))
- :bug: let context providers work without meilisearch ([0f86a69](https://github.com/continuedev/continue/commit/0f86a69e4a83458db2e20e404c26dac2e02355cf))
- :bug: llamacpp fix indexing max_tokens ([90590ab](https://github.com/continuedev/continue/commit/90590ab4e06fbc3fa721f73a4a922136946a756f))
- :bug: make sure server_version.txt exists ([17806d9](https://github.com/continuedev/continue/commit/17806d932502adbf974ccd93a670e57b78be9a08))
- :bug: make typegen.js windows compatible ([dc06228](https://github.com/continuedev/continue/commit/dc0622848b648ba27e7110b9b900673bb668ab4c))
- :bug: MAX_TOKENS_FOR_MODEL bug fix, more testing ([1c288f7](https://github.com/continuedev/continue/commit/1c288f7749747c6b1908ae16c977f80e5597d2ca))
- :bug: meilisearch fixes ([0de6e19](https://github.com/continuedev/continue/commit/0de6e1985d0e97ede5e19e7752a6be7cd2a5818d))
- :bug: more reliable download with request ([fdb036b](https://github.com/continuedev/continue/commit/fdb036bcecec891adaf99d73101c458fc4087406))
- :bug: more reliable setup of meilisearch ([12a8ae1](https://github.com/continuedev/continue/commit/12a8ae1c47f111b9f36633c96b26e8642c5ff223))
- :bug: now progress bar when api_key entered ([bf82c6f](https://github.com/continuedev/continue/commit/bf82c6fd16a6777f0a9bb68ce4879d7bab9019bb))
- :bug: number of bug fixes ([b9bdf58](https://github.com/continuedev/continue/commit/b9bdf5894c1c68b60d1919ae07b0f5909b00dec2))
- :bug: numerous small fixes ([0940d75](https://github.com/continuedev/continue/commit/0940d756dec3b98071ae5e5a12966e02420b3cd2))
- :bug: patch for ocassional 0 choices from older azure versions ([5c09b80](https://github.com/continuedev/continue/commit/5c09b8077588a447d6eaac9b7f624571be3ddb1d))
- :bug: permissions for pypi-deployment step ([b237850](https://github.com/continuedev/continue/commit/b237850c4b64435e26dfb5f12275a16a93e556a8))
- :bug: post-merge fixes ([96379a7](https://github.com/continuedev/continue/commit/96379a7bf5b576a2338142b10932d98cbc865d59))
- :bug: remove empty grammar from llama_cpp_args ([e5bbe3b](https://github.com/continuedev/continue/commit/e5bbe3bc4d59b6f35db1ce1b94be14244c11c766))
- :bug: replace hardcoded path for config file ([4fe9ace](https://github.com/continuedev/continue/commit/4fe9ace518bcdcf79999ce9938ba01b218d355e4))
- :bug: require socksio ([bba5e5e](https://github.com/continuedev/continue/commit/bba5e5e5b1da2dd924aa2632e38d4bb702bbbdd9))
- :bug: separately load ctx provs, fix filetree ([d8e821e](https://github.com/continuedev/continue/commit/d8e821e422678fd4248b472c7f3e67a32ecfefb5))
- :bug: set api_keys in config.py, fix spawn error handling ([6823307](https://github.com/continuedev/continue/commit/68233071dd0d97a353a66fe5627d69f97a389ca8))
- :bug: set export display in same step as linux npm test ([c3d62c5](https://github.com/continuedev/continue/commit/c3d62c5ae203aaca32583f75a7e80dfd9f196e11))
- :bug: small bug fix ([32d1149](https://github.com/continuedev/continue/commit/32d1149692c26eb966693f03db6d9cf496ba57a4))
- :bug: small bug fixes ([bc75ff2](https://github.com/continuedev/continue/commit/bc75ff294a2b5ec5eef5f77aff72aaa0c7f4a3f2))
- :bug: small fixes, update troubleshooting docs ([51fc07c](https://github.com/continuedev/continue/commit/51fc07cf6441d6330ce64e45e56e8f333ca309ed))
- :bug: solve EBUSY by polling ([9417973](https://github.com/continuedev/continue/commit/941797359f6554ac16a2e478047aabd5cbc0404b))
- :bug: ssh compatibility by reading from vscode.workspace.fs ([e5f5630](https://github.com/continuedev/continue/commit/e5f56308c5fd87695278682b2a36ca60df0db863))
- :bug: start meilisearch in parallel to server ([e4c1bb4](https://github.com/continuedev/continue/commit/e4c1bb4bedbe426d090f4bb2b8819ad935c5b3fb))
- :bug: stop streaming on rejection ([8d05fc2](https://github.com/continuedev/continue/commit/8d05fc2bb5c5df617800c1abcf43bb03c574482f))
- :bug: stop streaming on rejection ([9fc831e](https://github.com/continuedev/continue/commit/9fc831e7587cce99c8a6f2e56905c25068c8cab6))
- :bug: streaming url_decode for Ollama ([3690101](https://github.com/continuedev/continue/commit/3690101b790f91c749f208693aaffc00b9fa2a42))
- :bug: templating fix for queued LLM ([5c6609a](https://github.com/continuedev/continue/commit/5c6609ab5fa3a69cd0e3e8e61df643fcce1ecb47))
- :bug: temporarily disable lsp before fixing w/ vscode ([98f340b](https://github.com/continuedev/continue/commit/98f340bd97cba6f30cfe55d47419e3925b9dc679))
- :bug: temporarily remove replicate altogether ([bd79c00](https://github.com/continuedev/continue/commit/bd79c00e7790b92cfd8b8c8f8211b6c3d36e33a2))
- :bug: test and fix small issues with GGML ([72e8332](https://github.com/continuedev/continue/commit/72e83325a8eb5032c448a5e891c157987921ced2))
- :bug: timeout on blocking processes ([345b773](https://github.com/continuedev/continue/commit/345b7734d8c887d699d5038416d2a1f8193a33e9))
- :bug: traceback fixes, remove replicate from hiddenimports ([45d9bab](https://github.com/continuedev/continue/commit/45d9bab5cea745573be7112d7130089c596c88fa))
- :bug: try/except around starting meilisearch ([c867cd4](https://github.com/continuedev/continue/commit/c867cd40342d44901cf5277ded25f5dc5aaa4326))
- :bug: update search path for ripgrep on windows ([e428dc5](https://github.com/continuedev/continue/commit/e428dc53cedf54f394a7cddfe8a7ce7fbf469bb9))
- :bug: update the tip message for keyboard shortcut ([3558450](https://github.com/continuedev/continue/commit/355845002e178a618e9a792dd57b0649c3da8845))
- :bug: urldecode ollama responses, make edit faster ([19050f8](https://github.com/continuedev/continue/commit/19050f83228b3e7f08a6aacd5bdd1804a8315e4a))
- :bug: use certifi to set ca_bundle_path for openai ([3849420](https://github.com/continuedev/continue/commit/3849420948e491d5f84ac485169165d887751fd3))
- :bug: use posthog-node, not -js ([88a8166](https://github.com/continuedev/continue/commit/88a8166476d38889fd4f9323472cc34a5226e05c))
- :bug: use powershell remove-item ([64552dc](https://github.com/continuedev/continue/commit/64552dc881509c46aa14253ff94aee9d86ade256))
- :bug: use windows equivalent of rm -rf ([8d19866](https://github.com/continuedev/continue/commit/8d198663e116c7c77b7e59015bc6032736f71f6e))
- :bug: verify_ssl and ssl_context mutual exclusivity ([59b7453](https://github.com/continuedev/continue/commit/59b7453afed06418d4c171b65370a6a82f5a9221))
- :bug: version patch in the publish step ([1936f72](https://github.com/continuedev/continue/commit/1936f725d226bea2e13d5d88c1dd7a9a02ddd259))
- :bug: windowsHide on process spawn ([c3d31f0](https://github.com/continuedev/continue/commit/c3d31f00bb589df1c83308b7d9d69ed51c31341a))
- :bug: write out npm run package as package.js ([4636c95](https://github.com/continuedev/continue/commit/4636c9590154d6b5995948003da212eb25003750))
- :bug: write to local diff files ([6140d05](https://github.com/continuedev/continue/commit/6140d05e7d415d3334032c300ed593bdd181f7f5))
- :children_crossing: add slash commands to default config ([58e5dc4](https://github.com/continuedev/continue/commit/58e5dc4a5c4fcbed25170b61fbd88d479c5aebcf))
- :children_crossing: clear the dropdown after text input cleared ([23167a5](https://github.com/continuedev/continue/commit/23167a51d959fed5e4be057ceb9fff50cf34c6c8))
- :children_crossing: don't order meilisearch results by contnet ([ab7a90a](https://github.com/continuedev/continue/commit/ab7a90a0972188dcc7b8c28b1263c918776ca19d))
- :children_crossing: use default model in default config.py ([1bc5777](https://github.com/continuedev/continue/commit/1bc5777ed168e47e2ef2ab1b33eecf6cbd170a61))
- :construction_worker: copy_metadata for replicate in run.spec ([b0426d8](https://github.com/continuedev/continue/commit/b0426d82a4871e9081367ad4e977b22f42db5a89))
- :construction: working on fixing lsp ([1f95bb2](https://github.com/continuedev/continue/commit/1f95bb287846fc0501193d642420b574d9900857))
- :fire: remove version from package.json ([27c0a40](https://github.com/continuedev/continue/commit/27c0a403de28345cd03c39ad46c02f68ff57b3a1))
- :goal_net: catch errors when loading to meilisearch index ([7894c8e](https://github.com/continuedev/continue/commit/7894c8ed1517394aa00f6e496a97d9e27d204f5f))
- :goal_net: display errors in SimpleChatStep ([72784f6](https://github.com/continuedev/continue/commit/72784f6f1161f0c5b647889c26089a8247111dc9))
- :green_heart: cd extension before packaging ([0a6d72c](https://github.com/continuedev/continue/commit/0a6d72c099316e6cffa123d3ffa915f3fe13e770))
- :green_heart: cleanup file ([951552d](https://github.com/continuedev/continue/commit/951552dd0eede1f8f255aeaf5d34a13ff0c7bfb7))
- :green_heart: don't exclude jedi from pyinstaller ([a1328cb](https://github.com/continuedev/continue/commit/a1328cb5431f99cfe16b246ee4201b19530404e2))
- :green_heart: fix build scripts ([ab799b0](https://github.com/continuedev/continue/commit/ab799b0b0133e926ea06a1a12c092f42b9e053a1))
- :green_heart: fix copy statement to include.exe for windows ([36c3dfd](https://github.com/continuedev/continue/commit/36c3dfd51d319b9b9ad392988d13ef7f443e0937))
- :green_heart: fix preview.yaml ([a736d62](https://github.com/continuedev/continue/commit/a736d62f0e8b9b80ab9a949fd1739fb9a3be26e1))
- :green_heart: increase testing timeout to allow for fkill ([08b1cfd](https://github.com/continuedev/continue/commit/08b1cfdd2f6f456df7344c16f5d229a0ccfb841b))
- :green_heart: install rosetta ([5b9ef10](https://github.com/continuedev/continue/commit/5b9ef102973c608bc409a7b9ec244a4be1494e96))
- :green_heart: one last test ([758520f](https://github.com/continuedev/continue/commit/758520fe6b59d3330dec80ac07d05282d36e0058))
- :green_heart: only upload once per binary ([490838a](https://github.com/continuedev/continue/commit/490838a8ad920a52ada7e85675aefd965e978d77))
- :green_heart: package patch ([34f32ed](https://github.com/continuedev/continue/commit/34f32ed5f71055ea11d4332f18e77ceba5849631))
- :green_heart: package:pre-release ([d3f21da](https://github.com/continuedev/continue/commit/d3f21da803e36b78f968c2216d9f93f90ebabd6a))
- :green_heart: publish as pre-release! ([831bf5e](https://github.com/continuedev/continue/commit/831bf5e7f7c48dd80f71f1256f5597bd47bf22de))
- :green_heart: publishing to depend on ALL tests ([a131c17](https://github.com/continuedev/continue/commit/a131c17326591e67a68faf6f96371ad8fc332b71))
- :green_heart: pull origin main in main.yaml after pypi update ([6a9a079](https://github.com/continuedev/continue/commit/6a9a079914d94419183182cd0a5cc4439f2101ad))
- :green_heart: remove "patch" from vsce publish command ([d8327ec](https://github.com/continuedev/continue/commit/d8327ec6f82058479bd294bfcdccaf3c2b54de0a))
- :green_heart: remove npm_config_arch ([8c51664](https://github.com/continuedev/continue/commit/8c5166471b0d83b924d8bee1e0ca51822cc1bbdc))
- :green_heart: remove version from apckage.json ([5438ce9](https://github.com/continuedev/continue/commit/5438ce94406baa0f7d131ecacadefc72912dca0d))
- :green_heart: set permissions on apple silicon binary ([715cfed](https://github.com/continuedev/continue/commit/715cfed18747b6bc2e6d7bd7a977d249cc9066d5))
- :green_heart: testing ([14062c5](https://github.com/continuedev/continue/commit/14062c5c385bb7ba80096bf7daf6b6a5568b0b54))
- :green_heart: testing for failure to package dist in vsix ([19acf3b](https://github.com/continuedev/continue/commit/19acf3bb36c1e44274297c806b89b589ca02f5ba))
- :green_heart: update permissions and version ([c4ed41c](https://github.com/continuedev/continue/commit/c4ed41c861573f4c9bdff1a21ca3e056cfdd766e))
- :green_heart: update pylsp hidden import ([1e8ea65](https://github.com/continuedev/continue/commit/1e8ea654f5ad1e06bff2660b54a50955098703ba))
- :green_heart: update pypi version, don't push from main ([208eb65](https://github.com/continuedev/continue/commit/208eb65f67ccc62ce6d683fd9bed2fe9524b2136))
- :green_heart: use curl to download binary ([199e4b3](https://github.com/continuedev/continue/commit/199e4b3b99642ba5b1558132aa10119be1eeb525))
- :heavy_plus_sign: add bs4 to requirements.txt ([8a1e6fb](https://github.com/continuedev/continue/commit/8a1e6fb4adec6e5febb2a0d78eb0b2a01bfa028b))
- :heavy_plus_sign: add ripgrepy dependency to requirements.txt ([9801b50](https://github.com/continuedev/continue/commit/9801b50192ca661972d5b2997028db3cd0725fb7))
- :heavy_plus_sign: hidden import for replicate ([b75555f](https://github.com/continuedev/continue/commit/b75555f106be3c7612e7c31818ff674485096e4f))
- :heavy_plus_sign: include replicate in requirements.rtxt ([01b3f1f](https://github.com/continuedev/continue/commit/01b3f1ff8f4dd89ae79f15626ef5a3af2bc558c4))
- :lipstick: don't display entirety of large tracebacks ([a74eda5](https://github.com/continuedev/continue/commit/a74eda56cfcafb5c463a74df564ced6f882f8d3e))
- :lipstick: fix layout bugs ([b655781](https://github.com/continuedev/continue/commit/b6557810d70a7f341761d5018fa2835cc3a50af1))
- :lipstick: fix UI problems in vscode light themes ([5e8866d](https://github.com/continuedev/continue/commit/5e8866da83f8a97cb8492f26e175b948d0282262))
- :lipstick: logo alignment, better config failure description, patch ([c51ad53](https://github.com/continuedev/continue/commit/c51ad538deff06af6c9e5498b23e3536e18bfc4c))
- :lipstick: nicer autoscroll ([88699ff](https://github.com/continuedev/continue/commit/88699ff909b026511da392bf2c0a96be02abc6fd))
- :lipstick: small UI improvements ([ec4fb4d](https://github.com/continuedev/continue/commit/ec4fb4d9235151901c1f7367932ecc17ab55d8e4))
- :lipstick: ui tweaks to history + scrollbars ([6e8885f](https://github.com/continuedev/continue/commit/6e8885fc2f7feb06ef6ac87d2d7688f9f33d15de))
- :lipstick: update font size for input, remove first tutorial step ([73ff267](https://github.com/continuedev/continue/commit/73ff2678ad984c9d9082ec078a38450d5daa1376))
- :lipstick: update light gray hex code ([e0e0482](https://github.com/continuedev/continue/commit/e0e0482f2af2eadd3df72fbdb6974c07ba11c527))
- :lock: opt out of meilisearch analytics ([8db5b39](https://github.com/continuedev/continue/commit/8db5b39170229ba93b83f526e7fd80056e461c6a))
- :loud_sound: better logging for ggml completion endpoint ([0459b0c](https://github.com/continuedev/continue/commit/0459b0c919903852254ac2cd081307788884cd84))
- :loud_sound: fix logs to be sent from uvicorn ([d3b4103](https://github.com/continuedev/continue/commit/d3b4103cd2f639fc072b8a3269d7730478c8bb1c))
- :loud_sound: websocket logging and horizontal scrollbar ([7bb0fe3](https://github.com/continuedev/continue/commit/7bb0fe34bbc8affce0c675b88ffb79a6b9985860))
- :memo: escape <QUESTION> in docs ([7314e79](https://github.com/continuedev/continue/commit/7314e79ac5bc34936a2c3de0fd01aadbfe640e72))
- :memo: fix deployent readme ([7a38025](https://github.com/continuedev/continue/commit/7a3802523c2e5ae136c39849e2fbb0d3e7bba63e))
- :memo: remove reference duplicates for ctx providers ([043d695](https://github.com/continuedev/continue/commit/043d695198caed305fa6651918c3bbb2de87db36))
- :memo: small fix in troubleshooting.md ([275a03b](https://github.com/continuedev/continue/commit/275a03b7f1e32f57bd68e501074aa80e0dbed40f))
- :memo: use backup server links in docs ([815627b](https://github.com/continuedev/continue/commit/815627b167e4bf06308b51c6756e33c36b17b631))
- :pencil2: Fix typo that was causing automatic version bumping not to work for intellij ([3daf2c7](https://github.com/continuedev/continue/commit/3daf2c7b23caf838b862c2d2791ae8655b761d12))
- :rocket: fallback s3 bucket ([aa98080](https://github.com/continuedev/continue/commit/aa98080cb16c75d2b7d6d9771b97e63120052c62))
- :safety_vest: more safely convert windows path to posix ([4309f9d](https://github.com/continuedev/continue/commit/4309f9def89c25611273d99db01e7cc477ad935e))
- :white_check_mark: allow longer for python server to start in test ([d8f5f10](https://github.com/continuedev/continue/commit/d8f5f102f6f91487be0281316e581858ec4ca260))
- :white_check_mark: allow longer wait in test ([40ec1a3](https://github.com/continuedev/continue/commit/40ec1a31a7cd37da8b75bbabf1f0d160bb7bec5d))
- :zap: register vscode commands prior to server loading ([f7a3659](https://github.com/continuedev/continue/commit/f7a3659381f839b890f2c53086f7fedecf23d9ab))
- :zap: update count_tokens method ([8214203](https://github.com/continuedev/continue/commit/82142033f935d6236620d82e31a70ea8f2fb243e))
- 'inferenceConfig.stopSequences' failed to satisfy constraint: Member must have length less than or equal to 4 [#2538](https://github.com/continuedev/continue/issues/2538) ([90e994d](https://github.com/continuedev/continue/commit/90e994db3c106f9b63bd043203111e5e101071ba))
- (very) small typo breaking the prompt file examples link! ([bd6f9b9](https://github.com/continuedev/continue/commit/bd6f9b969647ee8cf6138dca63538063414553bb))
- `REPLACE INTO` code_snippet table ([bdb967d](https://github.com/continuedev/continue/commit/bdb967d7b3c18fa2e0237f3cc0e0ea7415102715))
- 🐛 Codebase Indexing still not work ([955ab93](https://github.com/continuedev/continue/commit/955ab93efc3f488127d16673b89f3900f75c2007))
- 🐛 typo in core.py ([#429](https://github.com/continuedev/continue/issues/429)) ([705324e](https://github.com/continuedev/continue/commit/705324ed2ef588b2885c0b03107b9e30ae358dae))
- a bunch of bugs, commit residuals such as npm install pg ([6d74e6b](https://github.com/continuedev/continue/commit/6d74e6b1fedf18b11ea12ba38164fcd146fdba4b))
- actions ([653ca20](https://github.com/continuedev/continue/commit/653ca205dfbe2f7fec94891c99053d393d3efd0c))
- add .mvn/ to list of default ignored folders ([ec91021](https://github.com/continuedev/continue/commit/ec91021adfcbd49becc3fdfbee6784981238a9f4))
- add 'rich' module to requirements.txt ([#612](https://github.com/continuedev/continue/issues/612)) ([5d21bdf](https://github.com/continuedev/continue/commit/5d21bdf2930b30723f1fd80b05d8c1c2ad589bb2))
- add checkmark icon to indicate selected model in dropdown ([98a3219](https://github.com/continuedev/continue/commit/98a321939ea1ba551025b16dc09f13e7a5e980ca))
- add code range for quick actions/fixes ([#1687](https://github.com/continuedev/continue/issues/1687)) ([9f2e9bc](https://github.com/continuedev/continue/commit/9f2e9bc2dff474447d8502e386bb0cc804730bb9))
- add context provider ([ae888d9](https://github.com/continuedev/continue/commit/ae888d9e814cf10931222070ed1e2ae5438325fe))
- Add context/getSymbolsForFiles endpoint in JetBrains and handle symbol retrieval errors ([ba1fdf7](https://github.com/continuedev/continue/commit/ba1fdf7bac07afe04e62bd659a87dc89800a302b))
- Add ContinuePluginDisposable to avoid memory leaks in Jetbrains ([a48a150](https://github.com/continuedev/continue/commit/a48a150b2024fca63cb50fb09a914a391c0cfce5))
- add Delphi/Pascal syntax highlighting support ([cfcb06b](https://github.com/continuedev/continue/commit/cfcb06b1763b17e9e6d150963e090523c372d5d6))
- Add directory checks and optimize token usage ([79736c3](https://github.com/continuedev/continue/commit/79736c3108313204184bb7d833ac21e23341354f))
- add focus to InputToolbar on click ([#703](https://github.com/continuedev/continue/issues/703)) ([6b17de4](https://github.com/continuedev/continue/commit/6b17de49ed0e991221baee986f9dbb758d55f291))
- add hover effect and restrict clickable area for the history and more back buttons ([9662b18](https://github.com/continuedev/continue/commit/9662b18a1ff36d8eaf743003f4df501245a31be2))
- add missing eos_token for qwen2.5-coder ([6a7eea2](https://github.com/continuedev/continue/commit/6a7eea27ca5d1ade790ce08dc1cf4eb7bbdeb585))
- add missing import ([400be9b](https://github.com/continuedev/continue/commit/400be9b47f0b8ab52b4e32ca77339497c63c8f96))
- add mistral model options to config schema ([d56b48c](https://github.com/continuedev/continue/commit/d56b48cad2fcb0bb8e4a3e0dd3e32ab7f704d762))
- add Msty logo ([397dcf4](https://github.com/continuedev/continue/commit/397dcf4ccc663560a49ff41e5892a5c118bce02e))
- add new UI breakpoints ([5105645](https://github.com/continuedev/continue/commit/5105645dedec92abd3fc613777f60fd2a638a0ab))
- add unit tests for commandExtractor and improve multiline comment handling ([0d940ec](https://github.com/continuedev/continue/commit/0d940ec6d1377b38945bd6217040cd025f89a80c))
- add uuid to chat msgs for deletions ([f63fd5d](https://github.com/continuedev/continue/commit/f63fd5d83cefbbe01721515a8f28f435353bd804))
- addTag ([53f886f](https://github.com/continuedev/continue/commit/53f886fd40c7bca29de06156f0e713a9700cc189))
- allow downloading missing embedder on Ollama, in VS Code ([1dc58c6](https://github.com/continuedev/continue/commit/1dc58c63ab174f579e860d778ae85e73f6fb8c9d))
- always show close icon ([4faed0b](https://github.com/continuedev/continue/commit/4faed0bc17a2b79e24fe5133efc40868bccaa6aa))
- apply actions in toolbar ([cd810eb](https://github.com/continuedev/continue/commit/cd810eb93939bccc5bc0ff1e5c54572c7d27a97d))
- apply insertion bug ([ceb9277](https://github.com/continuedev/continue/commit/ceb92773e9a6e4da27aa2de8f871c07f1e23d1a7))
- apply notch filename trunaction ([033ade0](https://github.com/continuedev/continue/commit/033ade024ed685ef66dad53e675449a47e2dc872))
- artifact name ([85e3fbc](https://github.com/continuedev/continue/commit/85e3fbc1c73872b1dfd8aee1f1dcef26b9f37761))
- artifact upload ([1039e77](https://github.com/continuedev/continue/commit/1039e777c8a081ed9362f29e20c7c6133f3b07cd))
- attempt to fix [#485](https://github.com/continuedev/continue/issues/485) ([#498](https://github.com/continuedev/continue/issues/498)) ([1188dd7](https://github.com/continuedev/continue/commit/1188dd7e5f26ed57d034c927ba032739963b9abc))
- auth ([72636d9](https://github.com/continuedev/continue/commit/72636d96c8d8bb303e66f2344f5eeef26f078816))
- autocomplete label after selecting a query type context provider ([#1562](https://github.com/continuedev/continue/issues/1562)) ([6407458](https://github.com/continuedev/continue/commit/6407458d7626d52558ea760b79a03129caba14ff))
- autocomplete logging + input layout ([c542e5d](https://github.com/continuedev/continue/commit/c542e5d6153078f42e8eaf4361a4e5bdf24d9bc2))
- Autocomplete not working when lookup is selected ([346f8fe](https://github.com/continuedev/continue/commit/346f8fe191a96eab5903b9a9f5a1e7e5ea1e3ee2))
- avoid parsing md rules as yaml ([6c24132](https://github.com/continuedev/continue/commit/6c24132fab5b1c828aeb2e162c0f07ab6c830d0d))
- binary ([8f24197](https://github.com/continuedev/continue/commit/8f2419740044a5df2ca70905c2b9ecd0fef3389e))
- broken build ([d09711a](https://github.com/continuedev/continue/commit/d09711ab5895aca1c91552e7d9140b3aa0535d77))
- broken help center quickpick ([a83526d](https://github.com/continuedev/continue/commit/a83526d6a49df6589d107695f99c03d02b68629c))
- broken imports ([5fd8d2c](https://github.com/continuedev/continue/commit/5fd8d2cae5926418116031dbab8ce9465bc4c8f9))
- broken imports ([f60009c](https://github.com/continuedev/continue/commit/f60009c2ea7c1c1a980152e5aa3cda17c3f6e64d))
- broken JB build ([0e2587c](https://github.com/continuedev/continue/commit/0e2587cc71f497141ccd5fc807624eac80f8f183))
- broken link ([6a675d8](https://github.com/continuedev/continue/commit/6a675d8edce1e430faa32b5142554bf61b843e0a))
- broken schema config ([0529ae8](https://github.com/continuedev/continue/commit/0529ae8e1e4f61d83f2a4c43c9b9d23a84192ec9))
- browse functionality ([ef5d753](https://github.com/continuedev/continue/commit/ef5d7535f0860978baeb337930f266b1201a7a5c))
- build issues with jetbrains ([267961f](https://github.com/continuedev/continue/commit/267961f5b67f8f4529917fc43ebc1df88db92b51))
- bump extension tester version ([006e1e4](https://github.com/continuedev/continue/commit/006e1e4b3bad2a8545a492cbeeb3238a1bf3345b))
- bump node.js dependencies ([18fd7fb](https://github.com/continuedev/continue/commit/18fd7fbe58a03dfda382f755e261c5f16e88f38e))
- cache ([368d264](https://github.com/continuedev/continue/commit/368d264d221793b7f2439678cef9b8b032135529))
- caching ([1e7aa1a](https://github.com/continuedev/continue/commit/1e7aa1a879c80c0acddf09fbe0d887b7ec9bd31d))
- change from llama3.1-8b to llama3.1:8b ([ea060a3](https://github.com/continuedev/continue/commit/ea060a37567af620cbdb8d665beb3f36cfeada6d))
- chat tests ([7be3bb4](https://github.com/continuedev/continue/commit/7be3bb4161e7a17f7b0ae6e8d3ce365479e70dde))
- ci ([1f47f54](https://github.com/continuedev/continue/commit/1f47f5436dde900b04cf1b307efa64f0fecebd59))
- ci ([f6f2633](https://github.com/continuedev/continue/commit/f6f263375be129e438ab46a3ddbbba0722d3eb24))
- ci ([fa9f86b](https://github.com/continuedev/continue/commit/fa9f86b6fd9ddb8cfab7dc52772c9f5a0623d654))
- CI checks + fix for broken config-yaml types ([2032a8c](https://github.com/continuedev/continue/commit/2032a8c31ce9f30a5996a0fa6566d840a1eae21b))
- ci e2e tests ([ade32d4](https://github.com/continuedev/continue/commit/ade32d428c5ef0fc8f5daefd52571b95387002f3))
- ci tests ([27d20eb](https://github.com/continuedev/continue/commit/27d20eb08b25b142a54641048670b036e201ea1d))
- circular state in redux selector ([5921bfa](https://github.com/continuedev/continue/commit/5921bfac67237dd4ea34dc1aad7856b220adf062))
- cleanup model select ([1933a29](https://github.com/continuedev/continue/commit/1933a29ec4536d4887130703f1b0f94c3cd3db8d))
- Clear all diff blocks before streaming and ensure dispose is run on EDT ([41357cd](https://github.com/continuedev/continue/commit/41357cd00c72b61cd54b80aa3963d047c0bb4184))
- Clipboard is not available in chat window ([ef26b7c](https://github.com/continuedev/continue/commit/ef26b7c80eb0cc503017df8b9abd634f868a9b89))
- close extension after tests ([575a707](https://github.com/continuedev/continue/commit/575a70721b39e41bfe8b860824d5eb606ce29ba9))
- close sidebar when cmd+l pressed on focus ([93c9507](https://github.com/continuedev/continue/commit/93c95075425179d27421c7b54d92c9235d914c3c))
- Cloudflare Workers AI message handling ([7eff255](https://github.com/continuedev/continue/commit/7eff25595c6aa063db37fb2d5394447c8472ba20))
- cmd+shft+l closes sidebar if focused ([#1638](https://github.com/continuedev/continue/issues/1638)) ([92b5c4c](https://github.com/continuedev/continue/commit/92b5c4ccf64a88c461bb32abc9ab02329651e6be)), closes [#1536](https://github.com/continuedev/continue/issues/1536) [#1456](https://github.com/continuedev/continue/issues/1456) [#1564](https://github.com/continuedev/continue/issues/1564) [#1576](https://github.com/continuedev/continue/issues/1576) [#1570](https://github.com/continuedev/continue/issues/1570) [#1582](https://github.com/continuedev/continue/issues/1582) [#1600](https://github.com/continuedev/continue/issues/1600) [#1618](https://github.com/continuedev/continue/issues/1618) [#1626](https://github.com/continuedev/continue/issues/1626) [#1637](https://github.com/continuedev/continue/issues/1637)
- code automatically expands after pressing enter ([42c02be](https://github.com/continuedev/continue/commit/42c02bed5fb61f925eb829cd8e7e39425225cc30))
- codestral template ([d56a33e](https://github.com/continuedev/continue/commit/d56a33ea06f7c9724ffaef86d71bbb8d31840fc8))
- colocation ([fb28272](https://github.com/continuedev/continue/commit/fb282727e92237f6b14e28e3538e04e13b837f1e))
- colocation ([e1d227c](https://github.com/continuedev/continue/commit/e1d227cbc3ea56e607fce5c5ce585c2b901f6029))
- comment out auto apply on MFE ([f61476a](https://github.com/continuedev/continue/commit/f61476a4936f364b4d0adc679da067d5f3ee8411))
- completions ([a2f62da](https://github.com/continuedev/continue/commit/a2f62dad3b4df8a9a243964584eed148835a077b))
- config error handling ([170676a](https://github.com/continuedev/continue/commit/170676a46149a4e507f827aa566f4b1d39344e2e))
- config-types "useSuffix" to "useFileSuffix" ([fa3afd5](https://github.com/continuedev/continue/commit/fa3afd52e766c298a9bfa62c57e8a9b6e695b1d6))
- **continue:** update context for slash commands ([cfac639](https://github.com/continuedev/continue/commit/cfac639e44ff5ba13960bbed6756047a90ed93f2))
- convert `walkDir` to an async generator ([#1783](https://github.com/continuedev/continue/issues/1783)) ([6824497](https://github.com/continuedev/continue/commit/68244977d377e1706a20892abc059bf5cb70bc71))
- copy ([5e122b7](https://github.com/continuedev/continue/commit/5e122b76b3cf5f0f511101bb1b2cea1349fa3a7a))
- **core:** delete indexed docs if re-indexing ([acea620](https://github.com/continuedev/continue/commit/acea620043d2c48826266d4f1f73eaf42819f0c6))
- **core:** remove eslint config and fix errors ([#1457](https://github.com/continuedev/continue/issues/1457)) ([3e1c06b](https://github.com/continuedev/continue/commit/3e1c06b41b7b90a0cad026c0ba0433fb1be6d277))
- **core:** use `TextDecoderStream` for stream response ([#1498](https://github.com/continuedev/continue/issues/1498)) ([09d256a](https://github.com/continuedev/continue/commit/09d256ad562eda0919b9fa2853176319bf4eda36))
- correct formatting ([cbb5c21](https://github.com/continuedev/continue/commit/cbb5c21f591e078df186fd86865830cb8971be03))
- correct llama2TemplateMessages ([#855](https://github.com/continuedev/continue/issues/855)) ([f95a54d](https://github.com/continuedev/continue/commit/f95a54d9c29247680839129c54eae912ba5ca85d))
- correct package.json update ([02968fb](https://github.com/continuedev/continue/commit/02968fb1551c0a26af75340641312e1f9f5a1102))
- correct typo in stop sequence ([0dac76e](https://github.com/continuedev/continue/commit/0dac76edf5f35226cf6b3e99c83a7c02273cc05c))
- corrected typo in the info Alert paragraph ([be55052](https://github.com/continuedev/continue/commit/be55052d230b50635c43727127ee776e67bd4c20))
- cursor focus issue causing unwanted return to text area ([#1086](https://github.com/continuedev/continue/issues/1086)) ([c54cd7a](https://github.com/continuedev/continue/commit/c54cd7a26dd2e4e8743be86c6ac22dd1c8dca774)), closes [#1078](https://github.com/continuedev/continue/issues/1078)
- cursor position ([6284eee](https://github.com/continuedev/continue/commit/6284eeebef984ac1afd3846b606a7745c5dfb49d))
- cycling through chat messages ([b3ac143](https://github.com/continuedev/continue/commit/b3ac14332e8bd89237dfa41847701e3378487145))
- deepseek doesn't support 'https://api.deepseek.com/completions' URL currently. When user selecting code in editor, and then use 'CMD +I' command to let model to modify codes, 400 error happens. ([170b99d](https://github.com/continuedev/continue/commit/170b99d3261b7d2ef32ba9e03a51ac37bb1fb787))
- delete all code_snippets ([b9114bb](https://github.com/continuedev/continue/commit/b9114bbdab90de0dd167c88b57bd96b27ad03524))
- delete button default style ([5423d24](https://github.com/continuedev/continue/commit/5423d24c35c614b0970e472aafa1f1048bf0d970))
- delete old docs index on force re-index ([#1778](https://github.com/continuedev/continue/issues/1778)) ([0d632ea](https://github.com/continuedev/continue/commit/0d632eabbfd512c813e65212c3f5765bbc3fc8c7))
- dependencies ([384fb56](https://github.com/continuedev/continue/commit/384fb562e5aac023301d592a186982a4b575fe98))
- dependencies ([8efb3be](https://github.com/continuedev/continue/commit/8efb3be1b1f2253f9a7fd3035d8f0055de8f426e))
- dependencies ([cc063fd](https://github.com/continuedev/continue/commit/cc063fdd124d66515bdcb9e2c64f575b40cc99d8))
- dependencies ([fccae87](https://github.com/continuedev/continue/commit/fccae8732e1a4f447ff0ab650e9ae717c2022ffc))
- Desktop.browse on linux ([7a11446](https://github.com/continuedev/continue/commit/7a11446bfc5d4cb9c05f64f1377518456d30f530))
- disable completions in the commit box ([2b611df](https://github.com/continuedev/continue/commit/2b611df53ba716c2b550ab3b275c571eed9d2c65))
- disable indexing for unsupported CPUs ([a005aa7](https://github.com/continuedev/continue/commit/a005aa78ac75ac47be3e647e9e23b45eab7dafaf))
- disable lancedb cpu check ([b496777](https://github.com/continuedev/continue/commit/b49677711b4340f57ba671bacda301138c2909fb))
- Do not break completion for unsupported FS providers ([4d1a33f](https://github.com/continuedev/continue/commit/4d1a33fd794a7415b0cc3b8de1d349fbba5a01cb))
- do not filter context providers if not in editMode ([b2182b9](https://github.com/continuedev/continue/commit/b2182b9c8568a38d3af179909fb305e1459f5276))
- do not write to the cache if caching is disabled ([b29ed9d](https://github.com/continuedev/continue/commit/b29ed9d34aa7ae343a2fd0c767f315e46e9aa110))
- docs and install dependencies task for new directory structure ([#551](https://github.com/continuedev/continue/issues/551)) ([6ef8ae6](https://github.com/continuedev/continue/commit/6ef8ae6d5ab7efb1e928ff6474b6ac7a804a34cc))
- **docs:** contributing guide ([5a06dfe](https://github.com/continuedev/continue/commit/5a06dfe1fb865bed0ca76ff23816da6396a0b27d))
- **docs:** correct openai api key name ([9cc25c1](https://github.com/continuedev/continue/commit/9cc25c18c9fff5fc6ac4d56153667b0e8311920c))
- **DocsCrawler:** Add response.ok check and improve link splitting ([d613b5a](https://github.com/continuedev/continue/commit/d613b5a7da5ad15c8b4b7dfe949812045f69635c))
- don't override embeddings model name with UNSPECIFIED ([bc79388](https://github.com/continuedev/continue/commit/bc79388634ebcfcf2e38aa240441f9939e9de85a))
- don't return empty string ([403eb38](https://github.com/continuedev/continue/commit/403eb38b9e3e38c277daed4ac6a2b6ac54353570))
- dont toggle models when toggling assistants ([5bd8dd4](https://github.com/continuedev/continue/commit/5bd8dd4cb759bbabc35bdc00c66d88e4a175f7a8))
- double ssh retries ([c9e2ebc](https://github.com/continuedev/continue/commit/c9e2ebc4589f7a06e804daee7f2ce6d7b2b66ce2))
- dropdown with the height of the sticky div ([#605](https://github.com/continuedev/continue/issues/605)) ([a8d6ff0](https://github.com/continuedev/continue/commit/a8d6ff04e43a81a3c76373469ffebdfcfa8ac9f2))
- editor focus handling on click events ([#716](https://github.com/continuedev/continue/issues/716)) ([f1660a5](https://github.com/continuedev/continue/commit/f1660a533146c420253df6e2dec0e0d4a2a9ebf4))
- Eliminate Uncaught TypeError in dev tools ([d342825](https://github.com/continuedev/continue/commit/d3428256543bcc5ab269df93c949b56df92793f2))
- Empty file inserts incorrect content ([ba9027b](https://github.com/continuedev/continue/commit/ba9027baea44ca51d299804636771d1a084d5fa7))
- empty last message ([d3f7795](https://github.com/continuedev/continue/commit/d3f7795e6133fe7452c0626e6a54fb229bacbd7b))
- enable OSR for mac after validations ([db77b50](https://github.com/continuedev/continue/commit/db77b5020d9b93cb4a7504e213d81940dbee9eaa))
- end thinking for reasoning models when request is complete ([5109685](https://github.com/continuedev/continue/commit/51096858ab1ddc2bee534c1ea848be2b4bdcfb3f))
- ensure code preview uses one more backtick than in the selected code as a fence ([#742](https://github.com/continuedev/continue/issues/742)) ([9ce7770](https://github.com/continuedev/continue/commit/9ce7770066cf870d2d1f36883f5e867157373eda))
- ensure template variables are only processed for stdio MCP servers ([99b1ab2](https://github.com/continuedev/continue/commit/99b1ab2ebe51b4678722703922ecca023afa911c))
- Ensure that the state is cleared after refresh, and files->uris when the file changes ([abd8907](https://github.com/continuedev/continue/commit/abd890738111e51bf12a700f9e5e8e66a7c76235))
- Ensure valid line index for highlighter ([f86c95e](https://github.com/continuedev/continue/commit/f86c95ebdbd1851bcbf0e6f22d18a348a8a154ed))
- error ([4c7f918](https://github.com/continuedev/continue/commit/4c7f9182665a259fc6b9cf10c93e696e97400a2c))
- error printing bug leading to uncaught err ([3af4d6b](https://github.com/continuedev/continue/commit/3af4d6b1edeac113499cc9626cea75a5083b1030))
- exclude open-codestral-mamba from supported models ([c6591f8](https://github.com/continuedev/continue/commit/c6591f836ea73e1244a0e965111f7c599669d9fa))
- **extensions:** schema for db ctx provider ([#1534](https://github.com/continuedev/continue/issues/1534)) ([6fdad55](https://github.com/continuedev/continue/commit/6fdad553b6ac7f64c1bb23617dbe313e16174a56))
- failing azure llm test ([546b52a](https://github.com/continuedev/continue/commit/546b52a2df928687d56924f593108f5bb9235712))
- failing test ([ec2a72d](https://github.com/continuedev/continue/commit/ec2a72dfabbe7bd9c220b18b55145f431e19f3bf))
- failing test ([002ecc9](https://github.com/continuedev/continue/commit/002ecc9b519472e0968e9f227894f3143c87f1ec))
- failing test ([07b6b0c](https://github.com/continuedev/continue/commit/07b6b0cbefe86f2efa5488a33060ee9eb9f20e4d))
- failing tests ([88ccb74](https://github.com/continuedev/continue/commit/88ccb74f7e69cf816d27f15a318a42c11f6d2aa5))
- failing tests ([cb2e0a7](https://github.com/continuedev/continue/commit/cb2e0a7c0e9330d9b79a3e9878c851b9eba92ade))
- failing tests ([7b52ff7](https://github.com/continuedev/continue/commit/7b52ff74d4009cb85c32a5e9482bd3624931d21e))
- failing tests ([8d29d41](https://github.com/continuedev/continue/commit/8d29d4158d0fe21b0c0c4722e8d23715acf9ebdf))
- failing tests ([2b828b0](https://github.com/continuedev/continue/commit/2b828b0e7b455bf6b0f9ae5a6f6a705ab4cefe6a))
- field name fix ([0de1a2f](https://github.com/continuedev/continue/commit/0de1a2ff8937452815311c806a038a7f6d75fdf8))
- filepath replacements in prompt files ([#1939](https://github.com/continuedev/continue/issues/1939)) ([c0923a0](https://github.com/continuedev/continue/commit/c0923a0dd7ca3a6e9dcbd9b02bfc34250269d2ef))
- fix [#709](https://github.com/continuedev/continue/issues/709) ([#712](https://github.com/continuedev/continue/issues/712)) ([9ae07cd](https://github.com/continuedev/continue/commit/9ae07cd711d011faaf9a79952c0e213a6aa72e87))
- fix 100% indexing progress ([7f214ad](https://github.com/continuedev/continue/commit/7f214adcbdca33ebb7270f721fd57eeeb0bac1e8))
- fixed misallignment in tests caused by linter ([15e93dd](https://github.com/continuedev/continue/commit/15e93dd5d0dc3b5dcd00504833beacbddbfe44f3))
- format code ([1d000be](https://github.com/continuedev/continue/commit/1d000bec682d1d723bf6748f34c3dd86b684d775))
- formatting ([9efd0e1](https://github.com/continuedev/continue/commit/9efd0e128ef464d45379e84abf15e98ad1b24d40))
- formatting ([2284291](https://github.com/continuedev/continue/commit/22842919489ad272edfa182e9f8af3a26d9813ae))
- formatting ([4d70d3d](https://github.com/continuedev/continue/commit/4d70d3dcbd646b1aa1cb6e586439459dec2db462))
- free trial config.json updates ([905f064](https://github.com/continuedev/continue/commit/905f064543e71864eaeb4e039e5d55fe2da845fd))
- fullscreen gui retains context when hidden, fixed fullscreen focusing ([#1582](https://github.com/continuedev/continue/issues/1582)) ([679e26d](https://github.com/continuedev/continue/commit/679e26d4a8a81402d57a64d819f347d9f56d42e0))
- gemini tool calls with MCP ([9ca4ad4](https://github.com/continuedev/continue/commit/9ca4ad4afe04fbfc3a6e0e6d447dd81ff22b4369))
- getting diff ([9431131](https://github.com/continuedev/continue/commit/9431131f1cbbc5fe742c033a8baf970241c63378))
- **google:** remove unnecessary parameter ([#394](https://github.com/continuedev/continue/issues/394)) ([938c1db](https://github.com/continuedev/continue/commit/938c1db5b37d5332ff5d188f4fa79f3bc6b7549a))
- grab PATH for mcp connections ([60ec3ac](https://github.com/continuedev/continue/commit/60ec3ac452f0b25d176728dfe6e83e4f34ade745))
- grammar ([58feb0b](https://github.com/continuedev/continue/commit/58feb0b8622e5f64737594ccbfd94aff2e9687c5))
- **gui:** ctx rendering w/ renderInlineAs: "" ([#1541](https://github.com/continuedev/continue/issues/1541)) ([8a92f43](https://github.com/continuedev/continue/commit/8a92f4312693549a1590e99c70152b843d59b119))
- **gui:** passing style props on StyledTooltip ([41e1eb5](https://github.com/continuedev/continue/commit/41e1eb582bec8676fc973c3a2b76259344fbb317))
- **gui:** typo ([3d53809](https://github.com/continuedev/continue/commit/3d5380908292bb4373a122a802f2011dd0283f62))
- handle apiType for azure w/ proxy ([036e196](https://github.com/continuedev/continue/commit/036e19618b86600e4e9c5ad6e6ee5a98e55e15f1))
- handle closed webview on quick edit ([#1942](https://github.com/continuedev/continue/issues/1942)) ([fe05db5](https://github.com/continuedev/continue/commit/fe05db541dafc45c9870d52d9f15e812e1ea90ad))
- handle deleted blocks ([c1157db](https://github.com/continuedev/continue/commit/c1157dbf02f6750d385b28006c90202e6b159d5a))
- Handle empty addedLines in revertDiff ([f280319](https://github.com/continuedev/continue/commit/f28031902f41c4ae9f7cab01398216272950b7d0))
- handle line removal diff ([#1744](https://github.com/continuedev/continue/issues/1744)) ([6126eca](https://github.com/continuedev/continue/commit/6126eca35ab981c7b8cd6a56769dfe7cb9e69349))
- handle optional params in EditSlashCommand ([#745](https://github.com/continuedev/continue/issues/745)) ([0141229](https://github.com/continuedev/continue/commit/014122957ff6e087b03def436b95797e59c6f6cb))
- Handle status update in DocsService ([ac3df81](https://github.com/continuedev/continue/commit/ac3df814e45343b07f63c99e8710f4a6b185074e))
- handle when dir might be a file path ([e43d3bd](https://github.com/continuedev/continue/commit/e43d3bda6aa833bfe686b8c84dfa64d43fa08748))
- hardcode `fontSize` for tool call status msg ([f78969b](https://github.com/continuedev/continue/commit/f78969b147a8aecb5c9e5162d080cdafb3f4adb7))
- hmr issues with non-react component exports ([0e55c08](https://github.com/continuedev/continue/commit/0e55c08be3ef5dcaec5305f41ae871ae6903b6c7))
- hotkey for new session ([#572](https://github.com/continuedev/continue/issues/572)) ([1d3acd9](https://github.com/continuedev/continue/commit/1d3acd9f194802d78c99cd407bcb72543aa0ff19))
- **HttpContextProvider:** support all possible local servers via a library ([c3b2552](https://github.com/continuedev/continue/commit/c3b2552ed5e47eef8dc2a691d5f76f30b61ceafd))
- if rules ([a6dd5a8](https://github.com/continuedev/continue/commit/a6dd5a83dedcae099f320a2da9362490eadde299))
- ignore CSV files ([#1972](https://github.com/continuedev/continue/issues/1972)) ([1272f34](https://github.com/continuedev/continue/commit/1272f3402a1fc12e5eb0566ad4a4aadc3db7dc06))
- Implement RepoMapContextProvider ([48c442d](https://github.com/continuedev/continue/commit/48c442d1c687b15d5612786e87a09b016714abd6))
- import posthog as type, use inline import ([57a84dc](https://github.com/continuedev/continue/commit/57a84dcfe16e3cdebea54f7a0b0fe354f0d8ad2b))
- Improve error handling and ensure coroutines continue correctly in case of failure ([46bd573](https://github.com/continuedev/continue/commit/46bd573a4296bb8ce8dc4c43b7293ff575655e53))
- improve textarea ux ([#1901](https://github.com/continuedev/continue/issues/1901)) ([228bc30](https://github.com/continuedev/continue/commit/228bc30e895b87e06c170775974c49e2781d9ae0))
- include access to the Documents folder ([03839c3](https://github.com/continuedev/continue/commit/03839c30813f3242d9a1fce91f39af110d8d85a7))
- incorporate feedback ([98efbbd](https://github.com/continuedev/continue/commit/98efbbd80f075928a45334f8cc4103221c2656c9))
- incorrect scroll logic on inline edit ([ed3af3f](https://github.com/continuedev/continue/commit/ed3af3f3fc49f6678d8a1f940d06aa62bec1f4b5))
- intellij freezing ([1f602e5](https://github.com/continuedev/continue/commit/1f602e53c29eb28d98754252823ad8f40f839d1b))
- isRuleActive ([2b97efe](https://github.com/continuedev/continue/commit/2b97efe5c6d045b461c76068c0e06df298d95902))
- issue 3732 ([ce5d664](https://github.com/continuedev/continue/commit/ce5d664f828de8681ee7129dfec3c16cad667c46))
- **JB+GUI:** arrow keys in GUI on linux ([9110904](https://github.com/continuedev/continue/commit/9110904e7d4f727339666ce235388397b8c4ff99))
- **jb:** meta keybinding issues ([b4dad78](https://github.com/continuedev/continue/commit/b4dad78a727feed0ce18769a8d3f9cfd00a3bd57))
- **jb:** remove markup from `edit` model title ([f6d334a](https://github.com/continuedev/continue/commit/f6d334a190bf1132c33a68f472aaeb2e5389399f))
- **jb:** set `setOffScreenRendering` conditionally ([fd66ff7](https://github.com/continuedev/continue/commit/fd66ff7d464d3138a127e2035bca88f6d42c4402))
- **Jira Context:** add api abstraction ([cc9b960](https://github.com/continuedev/continue/commit/cc9b96038bb0b88156729d1c39e6eec1794fe76a))
- jira ctx provider ([9d24d34](https://github.com/continuedev/continue/commit/9d24d3402f3e9f9937bfdff70fa5c4fa120b2dbf))
- keyboard shortcuts test on linux ([e45aa16](https://github.com/continuedev/continue/commit/e45aa164c64a52936c7dd1c0467548aadf821639))
- layout alignment ([#1779](https://github.com/continuedev/continue/issues/1779)) ([6e8fc12](https://github.com/continuedev/continue/commit/6e8fc1247c44c344a48db9758437cb7b3028591c))
- lint ([f575721](https://github.com/continuedev/continue/commit/f575721980a596cdb08be8a2e472ca26e6012325))
- lint ([3ba1153](https://github.com/continuedev/continue/commit/3ba1153243753682ca19b26031c2c3c73394b5f2))
- lint ([03cd1a3](https://github.com/continuedev/continue/commit/03cd1a338f03fc1baa87feb02bd6e56111fc0b13))
- lint ([747d380](https://github.com/continuedev/continue/commit/747d3806a99abf7f55823c2fbc06c74448ba3c1c))
- linting error ([67ab058](https://github.com/continuedev/continue/commit/67ab05821191d75a201e516b725a679671f94734))
- linux key ([9618327](https://github.com/continuedev/continue/commit/9618327cf45687f18aacdc04d62da86ce2d1e520))
- linux test env ([9f2f9d8](https://github.com/continuedev/continue/commit/9f2f9d895b2fe6678f49e394e4657f044e2aef2a))
- listbox z-index issue ([1d2bfcd](https://github.com/continuedev/continue/commit/1d2bfcd4b2ad7c3afef97348c55a1c668610ea07))
- listDir ([a41a4bd](https://github.com/continuedev/continue/commit/a41a4bd2df5807bd216a4b23eabe8656ca098cdb))
- **llm/Ollama:** changed buffer handling like in streamChat() ([f1f8967](https://github.com/continuedev/continue/commit/f1f8967b6c4b422428fcad0575c0b24ae78bf34a))
- load last session when completing edits ([1e33400](https://github.com/continuedev/continue/commit/1e334008c08eb8796510ba6aaac434073c44b3b8))
- local build script ([#810](https://github.com/continuedev/continue/issues/810)) ([76fe78a](https://github.com/continuedev/continue/commit/76fe78a1a3c05b0d6dd5ed6d15575e20e797cfa6))
- log llm completition in edit command ([3094180](https://github.com/continuedev/continue/commit/30941800b04c59b9a45375d6934a4fb79cac0141))
- logs ([e3cf75e](https://github.com/continuedev/continue/commit/e3cf75e0779d3bc02a468ea6551e2499979c08f7))
- mac build issues ([9946a5c](https://github.com/continuedev/continue/commit/9946a5c05f85d20e2d766719a861eb0f8a55019c))
- major fixes to getTheme ([c6002ba](https://github.com/continuedev/continue/commit/c6002ba2ac905bd5439173e6430676bdc3594a87))
- make `env.apiVersion` required for azure openai ([4355618](https://github.com/continuedev/continue/commit/4355618079808bdfc3240022e79a0a9dd8457b02))
- matching context code ([64fb928](https://github.com/continuedev/continue/commit/64fb928b8ff9c0f7753a8b8d374abc179de1f4d5))
- mcp context provider bug with issue 2467 ([98f07be](https://github.com/continuedev/continue/commit/98f07bef818cf5acc4de2828cbeaa0b4b499fc53))
- MDX compilation issue ([aca82d2](https://github.com/continuedev/continue/commit/aca82d2ef36a5bd57119343790875e2acf7819b3))
- merge bugs ([009e578](https://github.com/continuedev/continue/commit/009e578416982a90af05539420a7137ccd2d03c1))
- messages ([0b07020](https://github.com/continuedev/continue/commit/0b07020d7dd9bf59ca1d804668a215174273625b))
- minor issues ([#581](https://github.com/continuedev/continue/issues/581)) ([570f61d](https://github.com/continuedev/continue/commit/570f61de5a404559b4ff69eb5cd2fa216e70e872))
- missing arguments ([452b242](https://github.com/continuedev/continue/commit/452b2422d18c05dd28dbfa6fd127f4c3e0b0223c))
- missing cd ([0d88b8f](https://github.com/continuedev/continue/commit/0d88b8f379d161962a72626fe2af583cce0f3fe0))
- missing Content-Type header in OllamaEmbeddingsProvider ([#1855](https://github.com/continuedev/continue/issues/1855)) ([841294d](https://github.com/continuedev/continue/commit/841294d15f3ad6ea4a9f7cf277fbcb905d9bd978))
- missing context items ([bdc2ec8](https://github.com/continuedev/continue/commit/bdc2ec8374ac9e7d14fc440ea80fa438005735e8))
- missing env ([1ebd3a3](https://github.com/continuedev/continue/commit/1ebd3a31bab5ed31374518022c5f1b7658c379f2))
- missing package ([e284cec](https://github.com/continuedev/continue/commit/e284cecd6584b14bee43794d0d238d64c734a10a))
- missing useLegacyCompletionsEndpoint in yaml schema ([a4b4395](https://github.com/continuedev/continue/commit/a4b4395649e9fa7888f8204ddcf56b88632b5b90))
- model name ([b1ec201](https://github.com/continuedev/continue/commit/b1ec2012c649ff29e84d6682234328e9c2fdb792))
- models and mode are not synced ([57441da](https://github.com/continuedev/continue/commit/57441dad6696eeb6d4bd6d769aec06e389367a53))
- modify access modifier public to private ([c36f233](https://github.com/continuedev/continue/commit/c36f23322265a0d9c28eb401acf49d76294a1249))
- move autocomplete logic off of EDT ([c46083d](https://github.com/continuedev/continue/commit/c46083d4115a79938bc6ae2b9421f36803b0a992))
- naming ([ba39842](https://github.com/continuedev/continue/commit/ba3984290ddb875608845904d31c5a5d57b1bf03))
- naming ([42a03ad](https://github.com/continuedev/continue/commit/42a03ad020634a7c6be719a70bf40144582682dc))
- naming ([fb1a75d](https://github.com/continuedev/continue/commit/fb1a75dcf33fa38863bda4f5b88c1a567aaf0269))
- navigation bug ([fab5347](https://github.com/continuedev/continue/commit/fab53472a931b49d7f0c19af22263df6b54bc292))
- not sure how that button fragment got there ([880f1de](https://github.com/continuedev/continue/commit/880f1de80d2bc33635cc9ebed01ae84e2c17c640))
- number of completions to generate is not supported ([db90b09](https://github.com/continuedev/continue/commit/db90b0954b36e856258eed2b61af8bf107f13510))
- old docs ([b52910b](https://github.com/continuedev/continue/commit/b52910b43351e5df8c07cfb27beca8fba5463074))
- ollama tool use ([82ac684](https://github.com/continuedev/continue/commit/82ac6848cabca21122995460328b8f4420ae3222))
- onboarding telemetry ([445dba3](https://github.com/continuedev/continue/commit/445dba3bb60654ac9c3f8d245f896d490574c3a1))
- only alert free trial if using free trial ([f0e73ef](https://github.com/continuedev/continue/commit/f0e73ef63a8fd8bb7ce313f9ed5439c7c68969bf))
- only perform lancedb cpu check for linux ([e0ee7d1](https://github.com/continuedev/continue/commit/e0ee7d1e446b18c05ed3166b0aa3b199ec021f1a))
- open new session from any page ([258bbc6](https://github.com/continuedev/continue/commit/258bbc6b5cbbd3b897e8045df247bb2a1fc21f94))
- overly eager suffix matching ([556a8c5](https://github.com/continuedev/continue/commit/556a8c5ee3f72311a384c64ba14fde7122e68741))
- path ([d01f243](https://github.com/continuedev/continue/commit/d01f24309b490799014dca0fd03775c39745d660))
- path ([3ecc89e](https://github.com/continuedev/continue/commit/3ecc89eaf55b970e1a494f88fed0850c4b85c565))
- pathing issue in jb ([c33e390](https://github.com/continuedev/continue/commit/c33e390427e459a5426ee7669733c2b407649e40))
- pause between tests to avoid db lock ([4cb84c1](https://github.com/continuedev/continue/commit/4cb84c11b09dac6eb0791e35b95ff6f571130391))
- placeholder ([8ded13a](https://github.com/continuedev/continue/commit/8ded13a8f8e54df4951b547d468782117fb11b8b))
- policy precedence ([de6a0ae](https://github.com/continuedev/continue/commit/de6a0ae36c43753e768bf0164dfbe1b938da790d))
- preserve indentation in code blocks ([#1554](https://github.com/continuedev/continue/issues/1554)) ([574a392](https://github.com/continuedev/continue/commit/574a3923a6f97d8995561da3654aae3aca9ae439))
- preserve system messages as user messages ([d60a1c1](https://github.com/continuedev/continue/commit/d60a1c1b1f3174cd501eca69746009bdeb4c6f7f))
- prettier ([4d278e4](https://github.com/continuedev/continue/commit/4d278e479ae02f0d02ccff8db2448d6b38b3d3e5))
- prevent autoscrolling on new tool msgs ([678a75f](https://github.com/continuedev/continue/commit/678a75f8f7b97b0f75407c6e1b1dd78c415d22eb))
- Prevent duplicate context provider addition ([2d6eacb](https://github.com/continuedev/continue/commit/2d6eacbeab8eb89ddc32311ade4efe3ccb272bca))
- Prevent multiple resumes in continuation ([1e96bfb](https://github.com/continuedev/continue/commit/1e96bfb9683fa0a45437f16b419be066917351cb))
- Promise arrays being returned. Added a new definitions-only context provider. fix: typescript tagging. ([#835](https://github.com/continuedev/continue/issues/835)) ([00eeb00](https://github.com/continuedev/continue/commit/00eeb0024178953172941bba293808be4ca5eed2))
- prompt format ([0f6d280](https://github.com/continuedev/continue/commit/0f6d280234c459e1935b62e5244f4577d64fac53))
- promptblock parsing ([eb6735e](https://github.com/continuedev/continue/commit/eb6735ee25da93e01a7b09223960b8975b41d898))
- Properly ignore ignored folders when generating /onboard context ([f7bca6a](https://github.com/continuedev/continue/commit/f7bca6a61d56b6e17b100294e605d0e2bbe0239b))
- protocols ([c8a24c0](https://github.com/continuedev/continue/commit/c8a24c0c9040a0da02bdd215b77b0320c160256f))
- qwen completions missing whitespaces ([75dce9c](https://github.com/continuedev/continue/commit/75dce9cbd14cc374d1ce4921522f3423202be7b1))
- **reg:** :ambulance: remove log except when in NODE_ENV === "test" ([f60fd1c](https://github.com/continuedev/continue/commit/f60fd1c6188567c67d3d8285fd6c2c5d6acc5bfa))
- regex ([05a3eec](https://github.com/continuedev/continue/commit/05a3eec21ed1c26aa4d9825e6be5e209cdb9563c))
- reject logic ([5944fca](https://github.com/continuedev/continue/commit/5944fca7374851869e469ed1cd3f13056de5c8b3))
- remove `any` from promptTemplate passing ([7834d26](https://github.com/continuedev/continue/commit/7834d266330625915542913f2d44ac0d80875d7e))
- remove debugger statement ([3e4c75b](https://github.com/continuedev/continue/commit/3e4c75bf461c36c2200ac2922670bb76a612b292))
- remove deugger ([3e8e29e](https://github.com/continuedev/continue/commit/3e8e29e8f2ce18e138df64441e436d3a07ffa45e))
- remove duplicated context comments ([0c217a4](https://github.com/continuedev/continue/commit/0c217a40082c2c244de4f4f8c955d96314f130da))
- remove gifs from media ([77d321f](https://github.com/continuedev/continue/commit/77d321ff82dcd34b55d0a0253eb45672be774fe1))
- remove invalid schema ([2355eb6](https://github.com/continuedev/continue/commit/2355eb62a5c3c5034b528ab17e8ad7a0b661bc7c))
- remove mismatch between last selected profile and current profile ([d9eb118](https://github.com/continuedev/continue/commit/d9eb118c514fc2a4364f532eb6438588fc9f712c))
- remove some backup files generated by pkg if present ([#1287](https://github.com/continuedev/continue/issues/1287)) ([9f160ad](https://github.com/continuedev/continue/commit/9f160ad11a1d36bfc92d2da2a8ea31721652b3e4))
- remove stale code ([cd97833](https://github.com/continuedev/continue/commit/cd97833be88ecaf3c2860dc41ca57c9e2b18ad48))
- remove testing logic ([5df8177](https://github.com/continuedev/continue/commit/5df81770df526c69dd71f2421a9052deb17accf6))
- remove tooltips when switching files ([200d291](https://github.com/continuedev/continue/commit/200d29103ea97db06283f2d4b5e0eb9b6afb86df))
- remove unnecessary baseUrl ([5f159fb](https://github.com/continuedev/continue/commit/5f159fbd81c4b8680cbad8b3875aa54e2079928e))
- remove unused argument ([7dd2c22](https://github.com/continuedev/continue/commit/7dd2c22632d50726271655d5c5d027a8219e6862))
- remove unused imports ([1b82d39](https://github.com/continuedev/continue/commit/1b82d39ac5b8e0cfb990a4968b3e8bc81af768ce))
- rename edit folder ([ab1ad0a](https://github.com/continuedev/continue/commit/ab1ad0ae48eeceee87043cae9a7862a1c4f70da0))
- replace null with empty string in getSidebarContent function ([#822](https://github.com/continuedev/continue/issues/822)) ([50d1188](https://github.com/continuedev/continue/commit/50d1188479be206d1a4ed5dcd258d4101ae4f8ba))
- report indexing errors in webview ([6a67514](https://github.com/continuedev/continue/commit/6a67514fcf108bd0eb6989777abaed02ce2e61e7))
- reset config errors ([cc199c2](https://github.com/continuedev/continue/commit/cc199c28634d006d38416533b06f26e001cea3fe))
- Resolve proxy error when adding Azure OpenAI model ([17dbfe6](https://github.com/continuedev/continue/commit/17dbfe6bfd38f67b7cb535da00fa626cdaf4f690))
- Resolve type error in env comparison ([45eacf4](https://github.com/continuedev/continue/commit/45eacf4ebbd99a97eef32eef01d780a1f8c57b7a))
- Resolved errors related to incorrect URIs ([0a7da20](https://github.com/continuedev/continue/commit/0a7da2067e8d8567084d05f69038b7ca95464d1f))
- restore all e2e tests ([d3a6eeb](https://github.com/continuedev/continue/commit/d3a6eebeb7b5d96133d1c13e533913dc5ec2f19c))
- restore cache ([91ea00d](https://github.com/continuedev/continue/commit/91ea00d284227f3875daf17591b15409004372ac))
- restore comments ([067a3a8](https://github.com/continuedev/continue/commit/067a3a843baf5a0e12bd5b6f21f37a504836286c))
- restore tests ([553c751](https://github.com/continuedev/continue/commit/553c751562afb736fb914549ba3d2c75e5a4b305))
- return ctx items from prompt file ([84df7f4](https://github.com/continuedev/continue/commit/84df7f45bf91c70da79901c0a44cc3b156655f6f))
- revert file changes ([7a44a38](https://github.com/continuedev/continue/commit/7a44a38c860cfbd0eeab35d8fdb3cb198edad680))
- Revert filepath changes ([3df34b7](https://github.com/continuedev/continue/commit/3df34b725e873fcd42058474d83ef634e21d5cad))
- revert launch.json change ([b3abfb8](https://github.com/continuedev/continue/commit/b3abfb80d71c16a0e51cc980d4262dbd218d6463))
- reverted the url of llama.cpp back to 'completion'. ([e8523a2](https://github.com/continuedev/continue/commit/e8523a2b07c7a5d1539138ee8e4a4836fea7a36b))
- rules ([bcae5b3](https://github.com/continuedev/continue/commit/bcae5b3cf210f1a8ced3b2c913961ed16aaf2ea9))
- rules ([9e57412](https://github.com/continuedev/continue/commit/9e57412fe161a9451aaaa651a9f9ed2d7b6b5517))
- rules ([f29110f](https://github.com/continuedev/continue/commit/f29110f98b1c71f4832d6b7ff0041e342e2a0c4a))
- rules ([b39cb83](https://github.com/continuedev/continue/commit/b39cb836ad6180226d39abe85f5637a6e2dac44d))
- sanitize lance table names ([5d3b2cb](https://github.com/continuedev/continue/commit/5d3b2cb5070505578cc16e67d2309a40a403bc94))
- scaffolding for future language syntax highlighting ([647656c](https://github.com/continuedev/continue/commit/647656cd3d007c016bea372bdfcb5d69f9d1d52a))
- scroll issues w/ code blocks ([#1688](https://github.com/continuedev/continue/issues/1688)) ([ceb8da0](https://github.com/continuedev/continue/commit/ceb8da0b20db70707a3443ff6f705bba6922488d))
- scroll to the bottom on history change ([b94ccc6](https://github.com/continuedev/continue/commit/b94ccc6db557f705ba4f810ed56c55ce21f383dd))
- selected profile id writing to global context in org profile rectification ([47755d1](https://github.com/continuedev/continue/commit/47755d1cba3b8c7cb5c0b9a19bf8ca0a92eec850))
- selector for `isSingleRangeEdit` ([badeb1c](https://github.com/continuedev/continue/commit/badeb1c88b7e1f8eb711a8a2cfc8f341ac22f67e))
- send feature flags ([2e3fdb3](https://github.com/continuedev/continue/commit/2e3fdb34e2e2358232cc12fcf0a0c2d16a3ef0cc))
- serve to localhost ([7eccf2d](https://github.com/continuedev/continue/commit/7eccf2ddb9308111686251474fb79fa03dfc87d6))
- set DEFAULT_CONTEXT_LENGTH to 8192 ([f163768](https://github.com/continuedev/continue/commit/f1637685c651180d1fc969c2f11712fda69eb9ee))
- settings persistance ([56f3803](https://github.com/continuedev/continue/commit/56f38038b35cedc2aa1bca1b09780b21c811bd83))
- setup ([6a93eae](https://github.com/continuedev/continue/commit/6a93eaeb0faf685dff216ecca850f9b2ef3cf2c3))
- show error is GH sign in failed in vscode ([e92e052](https://github.com/continuedev/continue/commit/e92e052b16fb91173aa4dc85d4e80fa569b8e60a))
- sign in ([fa5d2c5](https://github.com/continuedev/continue/commit/fa5d2c5aa2cdcc580486f81fde3ae69e9d3069e5))
- sign in btn + colors ([1b669bf](https://github.com/continuedev/continue/commit/1b669bf80e2d81e4aa19c4d3f2dca730ddd6d4f8))
- Skip duplicate checks at document end or blank text ([decd04e](https://github.com/continuedev/continue/commit/decd04eddd4965b9f76c7a9a3abd276d8d2e1692))
- snippets in fim prefix ([51e1c42](https://github.com/continuedev/continue/commit/51e1c4216baa38d73141aca1d1e681eabf044b4b))
- some typos ([#478](https://github.com/continuedev/continue/issues/478)) ([775d051](https://github.com/continuedev/continue/commit/775d051b4bcdbe07fbd38fae4d3e36e79234eb56))
- spread tool degs ([4ddd275](https://github.com/continuedev/continue/commit/4ddd2755996012677c15069648158bd4334fa3dd))
- sqlite binary build on fresh clone ([#1433](https://github.com/continuedev/continue/issues/1433)) ([d124aa0](https://github.com/continuedev/continue/commit/d124aa0534303b58b55bcc2de4ec460b4f499178))
- stream ([c497517](https://github.com/continuedev/continue/commit/c49751713248e27f060b6eee11179e13b14c5adf))
- system message ([c2707c6](https://github.com/continuedev/continue/commit/c2707c6988a9b6368f183f0602de0cc2872726da))
- test ([7266efb](https://github.com/continuedev/continue/commit/7266efb4e8490accd69ad5818bd9c35fe6f59e5c))
- test env path ([71a151f](https://github.com/continuedev/continue/commit/71a151f399d514497ee917eca521863d07b2a270))
- test timing ([878e211](https://github.com/continuedev/continue/commit/878e2116d6a97c31cad97432d785aaf3b6deb196))
- tests ([35c674c](https://github.com/continuedev/continue/commit/35c674c724c21c6dc0b699a37f868e68c62f1259))
- tests ([cafb2e9](https://github.com/continuedev/continue/commit/cafb2e93c7a54b4a35ecc8a209f53982b3cc26d4))
- tests ([b786591](https://github.com/continuedev/continue/commit/b786591415d79119845e01376bf5dc97c0676b23))
- tests ([ff543e8](https://github.com/continuedev/continue/commit/ff543e8b321e4e48a088ba296ebac4504b97356b))
- tests ([81acb44](https://github.com/continuedev/continue/commit/81acb446019a773ce2df98467e4dcb8e5c2a428e))
- tests ([deea8f1](https://github.com/continuedev/continue/commit/deea8f1f44403b6d7d7bb4236ff5acb8d38bca01))
- The selected item in inline chat is always the first one ([97bc074](https://github.com/continuedev/continue/commit/97bc07453ba067f0438192d8dc6cc1cc9a86d962))
- timeout ([b9f161f](https://github.com/continuedev/continue/commit/b9f161f6d8b4e7400dff25912881f41f472a9137))
- timeouts ([0bf9169](https://github.com/continuedev/continue/commit/0bf9169a968a42dfc083696b3e8be5f1997ad7e5))
- timeouts ([0c4d9eb](https://github.com/continuedev/continue/commit/0c4d9eb819b9f1a41cddd7d0b84dc0e21ee372e9))
- timeouts ([4c90f3b](https://github.com/continuedev/continue/commit/4c90f3bdd32761b7f34a6f05334dffc085805964))
- tip tap undo and redo ([88e34fb](https://github.com/continuedev/continue/commit/88e34fb69caae3750621f9f93253a7425535928f))
- tool use call ([c73f72f](https://github.com/continuedev/continue/commit/c73f72fbb379dae08aa41ac07608d9dc2aa8bc83))
- transpile dynamic imports to cjs ([#1975](https://github.com/continuedev/continue/issues/1975)) ([1e3e8eb](https://github.com/continuedev/continue/commit/1e3e8ebe408de1bbb5f9fe0dc61fefef3b85ca69))
- truncate `tagToString` to max filename len ([afb6bd0](https://github.com/continuedev/continue/commit/afb6bd0fb53505e29e961f5a78f0afe36c3b3071))
- ts ignore declaration file issue w/ dbinfoz ([#1945](https://github.com/continuedev/continue/issues/1945)) ([477ecd6](https://github.com/continuedev/continue/commit/477ecd63acc85be25ec992d4329f6460c42c6785))
- tsconfig ([8549981](https://github.com/continuedev/continue/commit/8549981e200e7e62f4a5895babc1c87c45b59db0))
- type errors ([f976e54](https://github.com/continuedev/continue/commit/f976e54dc3b2b8072aef3ecadf259ee1e5299956))
- type errors ([f575a36](https://github.com/continuedev/continue/commit/f575a3683c499f04766ac68af6b8e214ed2c3514))
- type errors ([cd82f4c](https://github.com/continuedev/continue/commit/cd82f4c61fdff90c8ed0e845a3b2ee8b0ce1be9d))
- type errors ([ea6b234](https://github.com/continuedev/continue/commit/ea6b2344d0b2cde7def58de549e604142a40a353))
- type errors ([f19b6b8](https://github.com/continuedev/continue/commit/f19b6b8abdb4dfb64c804efee851dee0bb00471c))
- type errors ([5c49505](https://github.com/continuedev/continue/commit/5c49505f2071504481c17976670d883efebddb4a))
- type errors ([cdc9600](https://github.com/continuedev/continue/commit/cdc9600e689161877ff7416f63baa9dcbfa6c18a))
- type errors ([7ee4d8d](https://github.com/continuedev/continue/commit/7ee4d8db505924a4d0edd5ef3ac49f4bb9a706b6))
- type errors ([70ae585](https://github.com/continuedev/continue/commit/70ae58594cd136e16a53fcfde88a227dbec1104f))
- type errors ([34489b1](https://github.com/continuedev/continue/commit/34489b14de0fc1c5cfb300a624b3b8571372e532))
- type errors / formatting ([9dba473](https://github.com/continuedev/continue/commit/9dba4730b958e05ffad6e0bab74effefd228cd6c))
- types ([7b9c5cc](https://github.com/continuedev/continue/commit/7b9c5cc4f062e2882706919b1effe34a460f45b8))
- types ([82cad59](https://github.com/continuedev/continue/commit/82cad596c85d0ee0b17ceccdf1f6cca5811b8f94))
- types ([5faecca](https://github.com/continuedev/continue/commit/5faecca09f27a7490b4050b7191197c74fe38299))
- types ([be27552](https://github.com/continuedev/continue/commit/be27552e3330b3a18ecb1065b6ab7210a4375826))
- types ([4b120e5](https://github.com/continuedev/continue/commit/4b120e543e7c2cec0d33714cfebe88800604fbfd))
- types ([44212e4](https://github.com/continuedev/continue/commit/44212e45828bb72c035bf63de4c13342935dcf0f))
- types ([72b253d](https://github.com/continuedev/continue/commit/72b253d14faf2d19832b0307fb4fba1e9aed11dd))
- types ([a88f5d7](https://github.com/continuedev/continue/commit/a88f5d771ced366926c9d6d821c697e52847a2f9))
- types ([41a7916](https://github.com/continuedev/continue/commit/41a79166aadc2bbbe80145d12bfeb1336d94c7e2))
- typo ([be874ad](https://github.com/continuedev/continue/commit/be874addb463f7db81c43b52f6e69f9de1c11fa3))
- **typo:** customize overview doc ([d4dfb69](https://github.com/continuedev/continue/commit/d4dfb699af1f5bffc923a5d850f877ed45ad74a6))
- undefined in title ([dcdfa59](https://github.com/continuedev/continue/commit/dcdfa59db43ae4e864e5cb1ca04b6804cca64d2f))
- unnecessary dependency ([ade83fd](https://github.com/continuedev/continue/commit/ade83fd0c6b3f2d3bb8175b6bdbdc0a6e9735c5b))
- unnecessary scroll delay ([183ae03](https://github.com/continuedev/continue/commit/183ae03ee37ba6629201211e9a63fbcb04ebc527))
- unset system message ([4480ee3](https://github.com/continuedev/continue/commit/4480ee3ac358e60182304e0e052c27ae45815e2f))
- update adf2md package ([ecbc123](https://github.com/continuedev/continue/commit/ecbc1234f7419f7921b31354594e5841363c54f5))
- Update Amazon Bedrock documentation and categorization ([b156d8f](https://github.com/continuedev/continue/commit/b156d8fd4c103e520ad720853752a4f9033ef68f))
- update CodebaseIndexer path ([#1870](https://github.com/continuedev/continue/issues/1870)) ([00d19e6](https://github.com/continuedev/continue/commit/00d19e623f8abb04fc4a022d354a909e2087aa3c))
- update imports in CodeSnippetsIndex.ts ([434e4ee](https://github.com/continuedev/continue/commit/434e4ee44b513db15d502c6a9d52ceb17fa805c3))
- update install script ([91f87f8](https://github.com/continuedev/continue/commit/91f87f8fd0ca328a91c9fd033c58fd5d2db02892))
- update intellij getDiff to return string[] ([1672d9f](https://github.com/continuedev/continue/commit/1672d9f864e99436b8f309af3529f2fda341172d))
- update snippets index to upsert cmds ([676f0d9](https://github.com/continuedev/continue/commit/676f0d9335df7bf7f42aae3434847cc0032c3c0a))
- update spacing ([a9d6597](https://github.com/continuedev/continue/commit/a9d6597a3fa054d093cb63fa5859e24b8f8d571b))
- update streamId matching ([8a1ce0f](https://github.com/continuedev/continue/commit/8a1ce0f1f06e92b7bd140d7eabd8393dc5993fea))
- update streamResponse function to use streaming decoding ([#1436](https://github.com/continuedev/continue/issues/1436)) ([2d1155d](https://github.com/continuedev/continue/commit/2d1155d7d4441042480bf928398b7a15a58d92d2))
- uri utils ([f1026c6](https://github.com/continuedev/continue/commit/f1026c6a2cfdd9424097d2103a5ffc2216e402d9))
- use `cmd` for windows MCP connections ([b8ca4cb](https://github.com/continuedev/continue/commit/b8ca4cb6ee91670e87ae331f5e4bab24a42adbe6))
- use bolt icon for shortcuts ([ff2116b](https://github.com/continuedev/continue/commit/ff2116bacc4537e09792ce9817b68e9cdfacb93e))
- use dir hash in `tagToString` ([9c2a780](https://github.com/continuedev/continue/commit/9c2a780fa986f5f98c3231a12971d3f16c7a8e74))
- use exponential backoff when checking ollama ([ebbc2fd](https://github.com/continuedev/continue/commit/ebbc2fd55df1fc43e1eacceba042fa4c0bc38e77))
- use ide scheme auth redirect ([bae74c1](https://github.com/continuedev/continue/commit/bae74c1b324b51c2e9b14b548f92557c75025f51))
- use index when sending chat input ([9398945](https://github.com/continuedev/continue/commit/93989459e3806f1ba6d647036dc9db064bf30e38))
- use introduction layout ([2b71869](https://github.com/continuedev/continue/commit/2b7186973695703a48daacea46c076231d308ddf))
- Use MCP server_name from JSON config as display name ([481d60a](https://github.com/continuedev/continue/commit/481d60a74d9b7cd0492aa5875d89e5b4e2e00c51))
- use new edit for "Generate code" cmd ([aac6f80](https://github.com/continuedev/continue/commit/aac6f805eb35107e7d8754413d955ccfeb964576))
- use prompt file sys msg ([093225d](https://github.com/continuedev/continue/commit/093225d4fed4d5249ca506b9867f5b5305ca4003))
- use proper alternating colors in KeyboardShortcuts.tsx ([f1bfcec](https://github.com/continuedev/continue/commit/f1bfcec8257c2b3f99fa99a643b0464239600490))
- vitest ([3bc4bb7](https://github.com/continuedev/continue/commit/3bc4bb710cfb2d9ef340a61f7c0ca27cb2540a28))
- vitest ([ceeb87e](https://github.com/continuedev/continue/commit/ceeb87e81b8848517dfc604025f11bf46e1955b0))
- **vscode:** handle null values for edits and page in getSidebarContent ([#618](https://github.com/continuedev/continue/issues/618)) ([f8a02ad](https://github.com/continuedev/continue/commit/f8a02add1775805246410ee3df396a4028ff2ec9))
- **vscode:** improve forceAutocomplete command reliability ([cc50768](https://github.com/continuedev/continue/commit/cc507687eb36be5b7fd59eac433a9a908c1ee07e))
- **vscode:** keybinding full screen toggle shortcut ([#625](https://github.com/continuedev/continue/issues/625)) ([2921504](https://github.com/continuedev/continue/commit/292150476c1cfcace2f6434d11c3a2dab1e5ccdb))
- wait for permissions before initializing messenger ([7d16b8a](https://github.com/continuedev/continue/commit/7d16b8a870ebf5348713fb116c109a646fe7c79e))
- wait for sidebar before other commands ([1566adc](https://github.com/continuedev/continue/commit/1566adcaeb3bc5258a563b208a511cdfb3938d7a))
- watcher ([f41c819](https://github.com/continuedev/continue/commit/f41c8194fc11da9c76de0531cccd73df78a4b1c2))
- watcher ([be153f8](https://github.com/continuedev/continue/commit/be153f8ab966e210d6249ff179c4ec1580507d88))
- whitespace in new line completions ([4db2637](https://github.com/continuedev/continue/commit/4db2637d5b65d427e959b7fd607a65ce4140c355))
- workflow ([e5fb4f1](https://github.com/continuedev/continue/commit/e5fb4f17ad9d3b6e72d6c7e0920357e607c0e776))
- wrap editor buttons when out of space ([#1727](https://github.com/continuedev/continue/issues/1727)) ([b38ec0e](https://github.com/continuedev/continue/commit/b38ec0e6153e9077d1e39dfd7a453746da31c75b))
- wrong shortcut for JB ([b358be2](https://github.com/continuedev/continue/commit/b358be2cfb798bfb6646f5015117199e2ad3a6e2))

### Features

- :adhesive_bandage: ca_bundle_path for maybeproxyopenai ([1018cd4](https://github.com/continuedev/continue/commit/1018cd47306f95dde35e1a0cc6b2a830444af389))
- :adhesive_bandage: QueuedLLM for simultaneous reqs (LM Studio) ([e9d2891](https://github.com/continuedev/continue/commit/e9d289173ec28a1a90ae58b1834c476bb46834b8))
- :art: custom prompt templates per model ([2e69e11](https://github.com/continuedev/continue/commit/2e69e117e198698f57bda06794cf030afbfe69e9))
- :art: small changes to /codebase ui ([1b68904](https://github.com/continuedev/continue/commit/1b689046a7f9323c7bd56e14d403675db0b38d54))
- :bricks: Enable terminals for additional vscode Remote Host Types ([1500281](https://github.com/continuedev/continue/commit/1500281bb547f7308aa7316f6783072e4f81fbc1))
- :bug: kill old server if needed ([a34046b](https://github.com/continuedev/continue/commit/a34046bbbe817f81cd6d8b7ff9025413589571aa))
- :children_crossing: display troubleshooting link when loading ([698dccf](https://github.com/continuedev/continue/commit/698dccf474619963de0312d36af6d01e3df8b47a))
- :children_crossing: drag continue to right bar tip ([04b1fde](https://github.com/continuedev/continue/commit/04b1fde6ea77e7698870063c3f588da93d763544))
- :children_crossing: keep file context up to data by listening for filesystem events ([#396](https://github.com/continuedev/continue/issues/396)) ([b6435e1](https://github.com/continuedev/continue/commit/b6435e1e479edb1e4f049098dc8522e944317f2a))
- :children_crossing: more keyboard shortcuts ([bd202df](https://github.com/continuedev/continue/commit/bd202df41755c581844d0ab1773ba55968b15450))
- :children_crossing: sort history by reverse date ([fd77a4b](https://github.com/continuedev/continue/commit/fd77a4bd6b255260d0f4cad11947b38f4d30030e))
- :children_crossing: tip to debug ([ec74169](https://github.com/continuedev/continue/commit/ec741697c42745d29539be08bc3e01dcd86b1643))
- :construction: create new sessions ([19060a3](https://github.com/continuedev/continue/commit/19060a30faf94454f4d69d01828a33985d07f109))
- :construction: first work on URLContextProvider ([6467759](https://github.com/continuedev/continue/commit/6467759012a139e76dcf022a681355f7d310a30d))
- :construction: react-router-dom work ([31e7c98](https://github.com/continuedev/continue/commit/31e7c9828f985eceb16b4c9c749cc5d4d9fd5beb))
- :construction: Router and new history page ([f19345c](https://github.com/continuedev/continue/commit/f19345c652cfcf1bdf13d0a44a2f302e0cd1aa4c))
- :construction: successfully loading past sessions ([c255279](https://github.com/continuedev/continue/commit/c25527926ad1d1f861dbed01df577e962e08d746))
- :construction: work on EditableDiv - better ctx prov. UI ([e33d579](https://github.com/continuedev/continue/commit/e33d579a1d2b643842827925d032c3de92cf5217))
- :egg: getting to know users form ([5dcdcd8](https://github.com/continuedev/continue/commit/5dcdcd81da2050825212e216bf5e7e69678d8c6e))
- :fire: fix duplication in reference ([1e3c8ad](https://github.com/continuedev/continue/commit/1e3c8adabba561eeef124144f3a2ef36d26334b4))
- :globe_with_meridians: alpaca chat template ([568771d](https://github.com/continuedev/continue/commit/568771d9b94280f1cb47aae863e8faf168eb052b))
- :green_heart: ship with binary ([2751dde](https://github.com/continuedev/continue/commit/2751ddeb2dd8150a0d7de6c5b65e275e1aa0e155))
- :lipstick: add context button (plus icon) ([2b35e5f](https://github.com/continuedev/continue/commit/2b35e5f5cff948ca7d4f207b23db68f0da248a95))
- :lipstick: add textgenwebui as official option in Ui ([09ac7a7](https://github.com/continuedev/continue/commit/09ac7a7fc07d915ac6f3ef96c8e8d1894b7719b9))
- :lipstick: better loading experience ([e19c918](https://github.com/continuedev/continue/commit/e19c918bb1c517a6a119ae8437c46e0724d2be9d))
- :lipstick: fixed footer and change button color ([48b8f1f](https://github.com/continuedev/continue/commit/48b8f1f0ad89a2b4e35f49c360576dd5aa99a7c0))
- :lipstick: gallery banner color ([0647a43](https://github.com/continuedev/continue/commit/0647a43a24c50ff0e52f23c49d979bddfcfbcd87))
- :lipstick: handful of UI improvements ([ceafdf1](https://github.com/continuedev/continue/commit/ceafdf18c9d9f0f8769d4a9e45c8a407179161c5))
- :lipstick: improvements to keyboard shortcuts ([7bf8e5b](https://github.com/continuedev/continue/commit/7bf8e5b56a518979bc1d2602b8eb4a2caf2b5fdb))
- :lipstick: more ui improvements ([f9a84bd](https://github.com/continuedev/continue/commit/f9a84bd2d65b3142cbcfcdd8e1e9394c9d4b458e))
- :lipstick: more ui improvements ([6e3ff01](https://github.com/continuedev/continue/commit/6e3ff0173e79f5374da9962c964559e0fb7165f5))
- :lipstick: nested context provider dropdown ([8d423fd](https://github.com/continuedev/continue/commit/8d423fd8d1d5b136e8138a906e8594ab93ec1982))
- :lipstick: nicer "continue server loading" UI ([8070ce1](https://github.com/continuedev/continue/commit/8070ce17c6d666436df38c684f5868ee7f689422))
- :lipstick: query input indicator for ctx provs ([4362a51](https://github.com/continuedev/continue/commit/4362a51214a683bfe1efd424ddb226d4e636eeed))
- :lipstick: setting to change font size ([4ea1760](https://github.com/continuedev/continue/commit/4ea176007d2228364d4d3fa4519898047cef988f))
- :lipstick: small ui tweaks, detached child process ([0181d62](https://github.com/continuedev/continue/commit/0181d6236d8b74c80adb62648fd6571431cf3210))
- :lipstick: sticky top bar in gui.tsx ([ef86d66](https://github.com/continuedev/continue/commit/ef86d661d54295c1abb9712557080f1838f96b33))
- :lipstick: UI Improvements! ([ae058c6](https://github.com/continuedev/continue/commit/ae058c6bac7ea37108e2894e419a22dfb95fd3ff))
- :lipstick: update icon and description ([92e7c9b](https://github.com/continuedev/continue/commit/92e7c9bb627a5559769e0eca23e02e106d2cfe96))
- :lipstick: update marketplace icon for pre-release ([cc98ad8](https://github.com/continuedev/continue/commit/cc98ad86d561d26c97dfdb24607a1d70afbcd2a1))
- :loud_sound: add context to dev data loggign ([8ac1518](https://github.com/continuedev/continue/commit/8ac15184aaa30d13bf168ff5123a12fb7a2dd39f))
- :loud_sound: display any server errors to the GUI ([daabebc](https://github.com/continuedev/continue/commit/daabebcc5d6df885a508582c0ca13e659305d2ff))
- :loud_sound: fallback unique id when vscode returns someValue.machineId ([c479442](https://github.com/continuedev/continue/commit/c47944260c5600e49d83568b3c4bafa3b7c2a37e))
- :loud_sound: give users access to Continue server logs ([5d97349](https://github.com/continuedev/continue/commit/5d973490687c40922f2b7a2ddf2a3e19c207eb0f))
- :loud_sound: light telemetry or context providers ([2959042](https://github.com/continuedev/continue/commit/2959042fa5a940aa4e8851b9d4db91f0f86092ff))
- :loud_sound: telemetry for vscode vs. jetbrains ([257cef6](https://github.com/continuedev/continue/commit/257cef697c93d2f2f59936587834908bd69ae842))
- :memo: embeddings experimental walkthrough ([e1ce1fe](https://github.com/continuedev/continue/commit/e1ce1fefee6a3f4c17ac568ba87cb7a4bcf65795))
- :memo: note about where session data is stored ([8ada89b](https://github.com/continuedev/continue/commit/8ada89b0f66f9e746394ee64591359537fe0c7f0))
- :money_with_wings: free trial usage indicator ([354a3f4](https://github.com/continuedev/continue/commit/354a3f493074b1fb63ff4f206a94c35f05673e99))
- :mute: complete removal of telemetry when allow_anonymous_telemetry false ([ae7dffa](https://github.com/continuedev/continue/commit/ae7dffa211af209aea2ca13b37729e390047dd7c))
- :necktie: allow timeout param for OpenAI LLM class ([404f7f8](https://github.com/continuedev/continue/commit/404f7f8089190d04c05957dc653baff44f342dc7))
- :recycle: load preset_urls at load_index ([3dabc4b](https://github.com/continuedev/continue/commit/3dabc4bd6c72e2d12afb059040ca75f606e47d9d))
- :rocket: headers param on LLM class ([e16b8ff](https://github.com/continuedev/continue/commit/e16b8ff5f2d9187f2b207addc1cd70d0cacbf9c8))
- :sparkles: [@terminal](https://github.com/terminal) context provider ([40cfabb](https://github.com/continuedev/continue/commit/40cfabb8ce8afe20e51ca4bafddc6a0b4755bf2c))
- :sparkles: /cmd slash command ([011877c](https://github.com/continuedev/continue/commit/011877c09e88ffcc715defc33e5c74c71ccc8aea))
- :sparkles: /share slash command ([4e38043](https://github.com/continuedev/continue/commit/4e3804351b76cc763d904f572ad525b651d8bc00))
- :sparkles: add edit templates to model packages ([1de976a](https://github.com/continuedev/continue/commit/1de976a6a11a0b945d59800b5a58f354808a49fc))
- :sparkles: add max_tokens option to LLM class ([ff2a397](https://github.com/continuedev/continue/commit/ff2a3978a1e2c95a4e288b56411bf0c32b86757b))
- :sparkles: add searchcontextprovider to default_config.py ([64f41fc](https://github.com/continuedev/continue/commit/64f41fc8a0a2616fe7074d0a49e7642fd462c95d))
- :sparkles: add stop_tokens option to LLM ([a16ba7a](https://github.com/continuedev/continue/commit/a16ba7a0166dbf9062ee4616e3ccfbff377e9f4b))
- :sparkles: Add support for Claude Sonnet 4 ([b24a76f](https://github.com/continuedev/continue/commit/b24a76fdabb5eccada838d8fe5ed5834c0120df1))
- :sparkles: add urlcontextprovider back to default config ([570891e](https://github.com/continuedev/continue/commit/570891e0201769defeabec95a58c997f6d5f3889))
- :sparkles: allow AzureOpenAI Service through GGML ([1343d12](https://github.com/continuedev/continue/commit/1343d1227cc86c860fb12695e64eaeae1384f72a))
- :sparkles: allow changing the summary prompt ([5c8b28b](https://github.com/continuedev/continue/commit/5c8b28b7fddf5b214de61102c768ef44d4087870))
- :sparkles: allow custom OpenAI base_url ([cb0c815](https://github.com/continuedev/continue/commit/cb0c815ad799050ecc0abdf3d15981e9832b9829))
- :sparkles: alt+cmd+d to automatically debug terminal! ([d0483ba](https://github.com/continuedev/continue/commit/d0483ba15b4ad13399a3385ae351cf33cca3db7f))
- :sparkles: auto-reload for config.py ([e652e90](https://github.com/continuedev/continue/commit/e652e90806b84eb409c496dd0903a4817243edc2))
- :sparkles: autoscrolling ([e6d369f](https://github.com/continuedev/continue/commit/e6d369f4312f0c8d175251e149c62d08608cb18c))
- :sparkles: back button on history page ([aafa5d5](https://github.com/continuedev/continue/commit/aafa5d5ec91a533f70d644e4d3fadd6f388c3e4b))
- :sparkles: change proxy url for openai class ([32a9a47](https://github.com/continuedev/continue/commit/32a9a477d33acd5cdde08164ebeb355b27a656b5))
- :sparkles: codelens in config.py ([58cd4db](https://github.com/continuedev/continue/commit/58cd4db2534aba9fed98925e68dc342efbc54fb0))
- :sparkles: Continue Quick Fix ([9af39a6](https://github.com/continuedev/continue/commit/9af39a67829a6770b93ffdaa6ea70af3125c7daf))
- :sparkles: Continue Quick Fix ([52cd93a](https://github.com/continuedev/continue/commit/52cd93ad73f7df6a5140b7d629e4f6e473dc0380))
- :sparkles: delete context groups ([2d3d96e](https://github.com/continuedev/continue/commit/2d3d96e5b55a225eb97251850909eb7a0a7242ed))
- :sparkles: diff context provider ([99db0da](https://github.com/continuedev/continue/commit/99db0da9d68c64d0b5adcab21e07c2db438c2404))
- :sparkles: display model params for previous prompts ([b1b30a4](https://github.com/continuedev/continue/commit/b1b30a459cbd589a471e1528ebfa9aaeb0514968))
- :sparkles: edit previous inputs ([c6a3d8a](https://github.com/continuedev/continue/commit/c6a3d8add014ddfe08c62b3ccb1b01dbc47495f5))
- :sparkles: EmbeddingContextProvider ([c6a1255](https://github.com/continuedev/continue/commit/c6a12550ffca1ffe35630e7aa9af6913ddbe0675))
- :sparkles: FileTreeContextProvider ([8bd76be](https://github.com/continuedev/continue/commit/8bd76be6c0925e0d5e5f6d239e9c6907df3cfd23))
- :sparkles: filter history by workspace ([0757bd2](https://github.com/continuedev/continue/commit/0757bd2b556996b9c434ac43e3e4a3b042ef5802))
- :sparkles: Give the terminal color and ansi escape rendering ([e83290b](https://github.com/continuedev/continue/commit/e83290bd3a921929052b0c0c91751800e7e9fd2c))
- :sparkles: highlight code on cmd+shift+L ([a1fdf16](https://github.com/continuedev/continue/commit/a1fdf164b776c5ff4ddfa1c4cad66e41de4254c0))
- :sparkles: huggingface inference api llm update ([bbf7973](https://github.com/continuedev/continue/commit/bbf7973ec091823c4197d59daaf151b748ee52fc))
- :sparkles: huggingface tgi LLM class ([a0e2e2d](https://github.com/continuedev/continue/commit/a0e2e2d3d606d8bf465eac541a84aa57316ee271))
- :sparkles: improved edit prompts for OS models ([1785e92](https://github.com/continuedev/continue/commit/1785e92f118b855f4a655d9b617d54b5c857a6ef))
- :sparkles: improved model dropdown ([2f792f4](https://github.com/continuedev/continue/commit/2f792f46026a6bb3c3580f2521b01ecb8c68117c))
- :sparkles: improvement to @ search rankings ([5590f63](https://github.com/continuedev/continue/commit/5590f63e42fda38d780bdc390361a65b65576498))
- :sparkles: llama-2 support ([72d18fb](https://github.com/continuedev/continue/commit/72d18fb8aaac9d192a508cd54fdb296321972379))
- :sparkles: LlamaCpp LLM subclass ([fc9e8e4](https://github.com/continuedev/continue/commit/fc9e8e4e325782409258dd483e36abf441051ee6))
- :sparkles: LSP connection over websockets ([a6d21f9](https://github.com/continuedev/continue/commit/a6d21f979fce6135fd76923478f76000b1b343cf))
- :sparkles: make follow-up edits ([73ae5d3](https://github.com/continuedev/continue/commit/73ae5d306c16d7c372e831d3ca41067a62c8481f))
- :sparkles: Make the terminal command aware of its OS, platform and shell ([1a786f6](https://github.com/continuedev/continue/commit/1a786f605da47abdfc66b02f4e88044ca95df960))
- :sparkles: manually running server option ([29940ea](https://github.com/continuedev/continue/commit/29940ea4223194cc32f6324534ad75db9e39305a))
- :sparkles: more model options, ollama error handling ([e2a7d4a](https://github.com/continuedev/continue/commit/e2a7d4a3c7832f8788feccf4168c13ec87a31fb2))
- :sparkles: ollama LLM class ([402883e](https://github.com/continuedev/continue/commit/402883e0661c24c418fb5aa93832c6f62dc97a63))
- :sparkles: refactor via search Step ([9cd249e](https://github.com/continuedev/continue/commit/9cd249ee007911037639281c6d7590889ad7b467))
- :sparkles: run continue immediately from pypi pkg ([70f6da9](https://github.com/continuedev/continue/commit/70f6da9b1ad190a967974fb477db669cbb5c928f))
- :sparkles: saved context groups ([c98f860](https://github.com/continuedev/continue/commit/c98f860460767fe14f8fbf139150b1bd1ee2ff12))
- :sparkles: Search context provider with ripgrep ([4b9dd4c](https://github.com/continuedev/continue/commit/4b9dd4cf8e853e17d92fb76fc726260d79e4bd7a))
- :sparkles: select custom model to use with edit step ([c1e5039](https://github.com/continuedev/continue/commit/c1e5039731941eb6b6eea166edd433cd49d4e858))
- :sparkles: select model from dropdown ([044b7ca](https://github.com/continuedev/continue/commit/044b7caa6b26a5d78ae52faa0ae675abc8c4e161))
- :sparkles: select model param count from UI ([105afec](https://github.com/continuedev/continue/commit/105afeccca903072bc48772bdaf8f100f996c4a7))
- :sparkles: set session timeout on GGML requests ([d04eec7](https://github.com/continuedev/continue/commit/d04eec7ee97319a6bcc48d289cd6eb3e0d9b8e19))
- :sparkles: set static urls for contextprovider ([d2b1aed](https://github.com/continuedev/continue/commit/d2b1aedcedf950d792baee202efdab199b05e57e))
- :sparkles: support browser-based IDEs with createMemoryRouter ([c9d96be](https://github.com/continuedev/continue/commit/c9d96be5615b9d193a1eeff9ab00da7ca0fe0b6b))
- :sparkles: support for Together.ai models ([8456b24](https://github.com/continuedev/continue/commit/8456b24318b13ea5d5dabec2328dd854f8a492b4))
- :sparkles: support stablecoder with replicate LLM ([d5e8688](https://github.com/continuedev/continue/commit/d5e86883f05fe3e99e1d6ff64241a48f935cc927))
- :sparkles: testing improved prompting for stablecode ([6112f26](https://github.com/continuedev/continue/commit/6112f26888086ccd47ca6bcfefdbc5b82ea86879))
- :sparkles: testing in ci, final test of ([cbd7656](https://github.com/continuedev/continue/commit/cbd7656bb4c9aebfe98c746111af52cf7192aa1b))
- :sparkles: text-gen-webui, cleaning config and welcome ([36d517e](https://github.com/continuedev/continue/commit/36d517e37d87b1bd39d6027577714b60c32e81e9))
- :sparkles: verify_ssl option for all LLMs ([e0522b9](https://github.com/continuedev/continue/commit/e0522b92cfa80491718de07928ce6a31850dab70))
- :technologist: bit of customization for DefaultPolicy ([3966790](https://github.com/continuedev/continue/commit/396679009fef21e13c1a6095212d1bd68e7f2a86))
- :technologist: button to view logs when loading ([b83eb52](https://github.com/continuedev/continue/commit/b83eb52c98d637ab3e3becf98aed9899821ea00d))
- :technologist: github workflow to measure issue/PR stats ([f75c423](https://github.com/continuedev/continue/commit/f75c42332c44c0d2a1a7e7a7ea32c2ef346df609))
- :white_check_mark: update test and add model telemetry ([3541d6a](https://github.com/continuedev/continue/commit/3541d6a770c919e1f2e55a1ae53c4fc3abe31aa7))
- :wrench: update default config.py imports ([e2d0baf](https://github.com/continuedev/continue/commit/e2d0baf39348597bdd1015897152f4e3bee0744d))
- :zap: queue messages before load, then send ([4c8b561](https://github.com/continuedev/continue/commit/4c8b56135b0a1862a4f1984e80aa1409f15e177d))
- :zap: reduce default max_tokens ([8fff1a8](https://github.com/continuedev/continue/commit/8fff1a811c477874482b65d014e4c5565d4a8649))
- `create_rule_block` tool ([2c8032c](https://github.com/continuedev/continue/commit/2c8032c3397f6ac8c26b8ce5e5b2fe89d079d3da))
- `description` in markdown yaml ([e4b70db](https://github.com/continuedev/continue/commit/e4b70dbfd843345ae0689a55802462473f5641dc))
- `globs` on rules and block docs ([24e22db](https://github.com/continuedev/continue/commit/24e22db9d416be54426709948564aee4aa08d54e))
- `requestRule` tool ([af30fbe](https://github.com/continuedev/continue/commit/af30fbe360def278cd56858d88c5373c80a2dab6))
- add "Gathering context..." indicator ([dd865ea](https://github.com/continuedev/continue/commit/dd865eadea014ccc8588a49e6d9c338d89734c9d))
- add "onboarding" slash command ([#1961](https://github.com/continuedev/continue/issues/1961)) ([5819ffb](https://github.com/continuedev/continue/commit/5819ffb43901eec8ad6af85737c28896f20c4e6e))
- add `globs` to create rule tool ([047e4e4](https://github.com/continuedev/continue/commit/047e4e418b7f252cb303f19992025acc4fb79d4f))
- add `index.ts` to sdk ([ae31c54](https://github.com/continuedev/continue/commit/ae31c54011ac365e560cb02b9851a6806641503a))
- add `no-negated-condition` eslint ([eb7d67d](https://github.com/continuedev/continue/commit/eb7d67dcf1a338c473a1bb7f787e4f35e62f8f73))
- add `stream` in defaultCompletionOptions yaml ([f72e293](https://github.com/continuedev/continue/commit/f72e293e2f6251e1cca632dffa7ec14554016a73))
- add `tsc:watch` cmd to vs code ([1d5e164](https://github.com/continuedev/continue/commit/1d5e164325e8fe8e66759baf2ac47ce766355faf))
- add accept/reject all btns ([011e2c2](https://github.com/continuedev/continue/commit/011e2c264407ff72a0bc90980ec7d8250da4bbd6))
- add agentinteraction dev data ([971c4de](https://github.com/continuedev/continue/commit/971c4de33e1a3f117eafe9d64bf175c0d2a849ef))
- add animated ellipsis to lump ([e14163c](https://github.com/continuedev/continue/commit/e14163cda2ff1de2e90621f1de2341fd90b136d2))
- add API key support for TEI reranker ([5dc25b5](https://github.com/continuedev/continue/commit/5dc25b5eb3b1f914d106a18bbb34f7d28e3039f3))
- add azure provider config ([#1764](https://github.com/continuedev/continue/issues/1764)) ([c9635de](https://github.com/continuedev/continue/commit/c9635def237e0bb4e1d899057e6b651b6a6cd1b2))
- add best experience onboarding ([8b30504](https://github.com/continuedev/continue/commit/8b305046eff820999e805c8b2cd6400e3572da1b))
- add chat scrollbar visibility configuration support ([14eaf32](https://github.com/continuedev/continue/commit/14eaf3272cde744ab6390d7113c2cb507e0d6734))
- add Claude 3.7 support to toolSupport.ts ([50ad91b](https://github.com/continuedev/continue/commit/50ad91bced864272c7f1bb92c13d7c910a0b6be2))
- Add cloudflare as provider ([ad8743a](https://github.com/continuedev/continue/commit/ad8743a9b563ae6e09cc140a0b3f9f202715e3b5))
- add code that comment is based on ([e3653f9](https://github.com/continuedev/continue/commit/e3653f98f08e52fa6a55d41ca147add1c8e5515a))
- add combobox for edit file selection ([7c6295f](https://github.com/continuedev/continue/commit/7c6295feeb32a478a2d8867f0a2b863583f6a97e))
- add deepseek models for novita ai ([a31c3eb](https://github.com/continuedev/continue/commit/a31c3ebc8334f931281cdb0a348150347c99d0e6))
- Add docs/getDetails endpoint in JetBrains ([da2dc1e](https://github.com/continuedev/continue/commit/da2dc1ed7de3caa9ae1e8b02a09526f57dd80bb8))
- add example integration test ([781a792](https://github.com/continuedev/continue/commit/781a792a5939958d05fa316261190c164871c563))
- add exponential backoff for API requests ([12ffa95](https://github.com/continuedev/continue/commit/12ffa956571acd12b383ba73560d3c42a7a4415c))
- add file search to quick edit ([#1714](https://github.com/continuedev/continue/issues/1714)) ([21d1b0c](https://github.com/continuedev/continue/commit/21d1b0c16dd9cd454d543e4b387f873e54d89aa5))
- add fixed version of the nodejs ([cd35857](https://github.com/continuedev/continue/commit/cd35857daf5577f4677f6675344fa8488e624352))
- add free trial card to onboarding ([#1600](https://github.com/continuedev/continue/issues/1600)) ([9bae5a2](https://github.com/continuedev/continue/commit/9bae5a254df25d25dc848b5c7bf69fc7189e6461))
- add gif to tutorial card ([e03eb9d](https://github.com/continuedev/continue/commit/e03eb9d5801985d1968d0528dd76767bf3907cbf))
- add gitlab context class ([efc1f3b](https://github.com/continuedev/continue/commit/efc1f3b9216d5bf503ecb58ff956f26a2bc4a130))
- add icon for URL ctx item peek ([e901cfd](https://github.com/continuedev/continue/commit/e901cfdf4ae383a2337c2e20182f6eee6d3010f9))
- add Jira context provider ([#860](https://github.com/continuedev/continue/issues/860)) ([8ba15b1](https://github.com/continuedev/continue/commit/8ba15b16665be871e037ada88d51b3403a8d094e))
- add Llama 3.1 8B to cloudflare provider options ([#1811](https://github.com/continuedev/continue/issues/1811)) ([bccbff2](https://github.com/continuedev/continue/commit/bccbff273c176c54f0209c5927a19b8c6d9375f9))
- add Moonshot AI model provider support ([b8a278d](https://github.com/continuedev/continue/commit/b8a278d216db9851aa2b0772e0313ff4bc12c7cc))
- add novita info ([94d8d39](https://github.com/continuedev/continue/commit/94d8d39cfc8d21f2544633330370d99ef17e0fdf))
- Add num_treads to ollama along with docs ([#863](https://github.com/continuedev/continue/issues/863)) ([d71721d](https://github.com/continuedev/continue/commit/d71721d71540cb084d67559e850440129faa67c0))
- add openai wrapper for sdk ([ab9a248](https://github.com/continuedev/continue/commit/ab9a248572a46e00f0fa6441af861dd260c4cb65))
- add OpenAI, xAI, Replicate and free-trial model options to config schema ([89a90f9](https://github.com/continuedev/continue/commit/89a90f99baaf493f184a09aa6083a606076f9771))
- Add prompt caching support ([24f9960](https://github.com/continuedev/continue/commit/24f9960e27ad065fec611661e69726c9be589042))
- add proxy support for ripgrep download ([d42c050](https://github.com/continuedev/continue/commit/d42c050e3645b4eb4635dd5a73ae12107adca701))
- add Quick Actions CodeLens feature ([#1674](https://github.com/continuedev/continue/issues/1674)) ([fdf3408](https://github.com/continuedev/continue/commit/fdf3408e0c9c2df749b5775d3c906abfdf40e799)), closes [#1536](https://github.com/continuedev/continue/issues/1536) [#1456](https://github.com/continuedev/continue/issues/1456) [#1564](https://github.com/continuedev/continue/issues/1564) [#1576](https://github.com/continuedev/continue/issues/1576) [#1570](https://github.com/continuedev/continue/issues/1570) [#1582](https://github.com/continuedev/continue/issues/1582) [#1600](https://github.com/continuedev/continue/issues/1600) [#1618](https://github.com/continuedev/continue/issues/1618) [#1626](https://github.com/continuedev/continue/issues/1626) [#1637](https://github.com/continuedev/continue/issues/1637)
- add Qwen2.5-Coder support ([387a76a](https://github.com/continuedev/continue/commit/387a76aa5bab873565de25a2d269f0b5b1a53f1e))
- add redux state for card logic ([5f32924](https://github.com/continuedev/continue/commit/5f32924becd3e8b03d19ffb4a5e4a2dcbfb6dbc3))
- add redux state for mfe ([6e5236a](https://github.com/continuedev/continue/commit/6e5236a58a896ab78faa1672e56237b4f4684877))
- add render util function ([413530a](https://github.com/continuedev/continue/commit/413530a029e32ab8e77520c0e5265cd37a84b24c))
- add rich quick pick for quick edit ([#1706](https://github.com/continuedev/continue/issues/1706)) ([f3b15eb](https://github.com/continuedev/continue/commit/f3b15eb1b14dcc3f4c28ebfb95150b0d6627cecb))
- add scope selector ([b78fdc4](https://github.com/continuedev/continue/commit/b78fdc4147e1ccb388b900a65099a2c61ba6ca26))
- Add signature column to code_snippets table ([a535e9e](https://github.com/continuedev/continue/commit/a535e9e15fb3041fec732574636221e0a83dccfa))
- add slash command cmd ([d4e1609](https://github.com/continuedev/continue/commit/d4e1609d2a65aeeece6caa8620a1b45196f40c98))
- add support for `baseSystemPrompt` ([f6957bb](https://github.com/continuedev/continue/commit/f6957bb856ba5915ad6351a518dc4b4a47536947))
- Add support for Cloudflare AI Gateway ([#1425](https://github.com/continuedev/continue/issues/1425)) ([837ad1c](https://github.com/continuedev/continue/commit/837ad1c552aef8909b9a1dbe01204ed2571134d7))
- add support for custom headers in SSE transport ([d392d77](https://github.com/continuedev/continue/commit/d392d7736d5d31804cd54b3c307bbfb6289e5358))
- add support for Mistral models in toolSupport.ts ([928cf2c](https://github.com/continuedev/continue/commit/928cf2c73752a23fe3fc026bbf3957c5c80d5604))
- add support for multiple MCP server types (stdio, sse, websocket) in the YAML configuration schema, ensuring backward compatibility with legacy configurations ([63a2ffe](https://github.com/continuedev/continue/commit/63a2ffe9696d90786f030c2af02cec2c463d914e))
- add tests ([0819bec](https://github.com/continuedev/continue/commit/0819becfd98127a187cc84484dd59bb4348a1ec3))
- add tutorial card ([#1716](https://github.com/continuedev/continue/issues/1716)) ([cb8b207](https://github.com/continuedev/continue/commit/cb8b207582dec6ce997c2c602c9998a5a64504db))
- add unified diff instant apply ([3c36b4d](https://github.com/continuedev/continue/commit/3c36b4dc378ddd8c5c5c2cc234e9a605d776d928))
- add Vertex AI support ([dfaa02c](https://github.com/continuedev/continue/commit/dfaa02c5051c44303e055675324ff6eaaac0e91b))
- allow input to exit ([18c5019](https://github.com/continuedev/continue/commit/18c50195248f6ca6c64e1eeb3cb8f10e6b85ffb5))
- allow JetBrains users to index docs ([#1797](https://github.com/continuedev/continue/issues/1797)) ([e0079a4](https://github.com/continuedev/continue/commit/e0079a43721f6002ff59e2eaa79dc8768a6f66f5))
- allow users to skip local onboaridng ([4685db0](https://github.com/continuedev/continue/commit/4685db02b854ab0a598defe0fdc4a68f8de50cc2))
- apply e2e tests ([12b3ed9](https://github.com/continuedev/continue/commit/12b3ed9291b4f46e920f8417f1e6585e45b7edd9))
- apply if state is active, onEnter callback dont working ([fed1651](https://github.com/continuedev/continue/commit/fed165161d1610db282b52230a4d914f4d2ffa62))
- apply waiting cursor to chat-input enter-button during prompting ([6d08361](https://github.com/continuedev/continue/commit/6d0836189cd1c18cf97b1e604adf4e5f201665b9))
- assistant select ([3b539d5](https://github.com/continuedev/continue/commit/3b539d53c39a98c361e27db6dfb72a8e14ddae50))
- assistant select refresh on right ([04f5e3e](https://github.com/continuedev/continue/commit/04f5e3e165fddf3101e6210b1111c0f261793054))
- **autocomplete:** recently visited ranged service ([811507a](https://github.com/continuedev/continue/commit/811507a406ed543adb4a8f876c3c3f30d8a9be35))
- better buttons for account dialog ([2a4c69f](https://github.com/continuedev/continue/commit/2a4c69ff87b64e21ad04edfed2aad7b048833b53))
- better editor content handling in edit ([7c72c4a](https://github.com/continuedev/continue/commit/7c72c4af878e4b683b582e40f483551314e92873))
- better error handling around ripgrep ([22f6fdf](https://github.com/continuedev/continue/commit/22f6fdfa1be6f84ab8548155465f863cb65a7e30))
- better type names ([43ebb1a](https://github.com/continuedev/continue/commit/43ebb1ad0a95ac1dfc0ff1e198fe53e8c50f0671))
- bookmark first 5 prompts by default ([0909ef9](https://github.com/continuedev/continue/commit/0909ef938b6d2928fa650c0e0613c1e106a0315d))
- brand text fix ([a25afee](https://github.com/continuedev/continue/commit/a25afeeda10c86072f80c025c20c21b167ddaaf0))
- bugfixes on redux schema updates ([4efd661](https://github.com/continuedev/continue/commit/4efd66137592916167f562ac45533dfffacd35cc))
- build steps for config-yaml ([73b95f3](https://github.com/continuedev/continue/commit/73b95f314703c266d9e348f9c1ba3dbbb3f3d266))
- cache org selection results ([13d5668](https://github.com/continuedev/continue/commit/13d566831db9e66ae270ff70e2b5c63304df6458))
- cache org selection results ([6e81720](https://github.com/continuedev/continue/commit/6e81720186d4215d6a650835f72ed1c6cc20e28f))
- capture base class and interfaces implemented ([df2bbc8](https://github.com/continuedev/continue/commit/df2bbc89da7f7f9ee406a8fe6d0c4fd244abd488))
- capture python definitions ([2f84ef0](https://github.com/continuedev/continue/commit/2f84ef0ab9a87aa6300415d30db02e3460a07195))
- change model desc ([8d3ecaf](https://github.com/continuedev/continue/commit/8d3ecafa3dbd9a3373ec7784eda1feac6b77b684))
- check for `nvm` version in install script ([b1a93b7](https://github.com/continuedev/continue/commit/b1a93b7a2dd22ab82ef9acdec5f1096c33c44c1b))
- cleanup apply manager ([4b9bac5](https://github.com/continuedev/continue/commit/4b9bac55ffae743d0221df65f0a14d0981f74aeb))
- cleanup inline edit code ([90d895e](https://github.com/continuedev/continue/commit/90d895e2e22ee40033c524a109b5777b5c3bf6cd))
- Client Certificate Options Support ([#1658](https://github.com/continuedev/continue/issues/1658)) ([136bf9e](https://github.com/continuedev/continue/commit/136bf9e0f0ce0193f64fe291922431808c32406e))
- close files ([d1eca9d](https://github.com/continuedev/continue/commit/d1eca9deeb3c1550982f679a58265593af603453))
- close tutorial listener ([d4d3102](https://github.com/continuedev/continue/commit/d4d3102b61c800576513cbb07c9d482fe973a0ef))
- collapsed codeblock by default ([979c247](https://github.com/continuedev/continue/commit/979c2473c61747157e65e1dcd33fd73ccfe404ad))
- config.json validation ([429e54e](https://github.com/continuedev/continue/commit/429e54ed347446747d206a97cb80c91baf5d407e))
- configure docs through config.json ([#1864](https://github.com/continuedev/continue/issues/1864)) ([d7dbdff](https://github.com/continuedev/continue/commit/d7dbdfff485f3970b8595c5a2680a012443747a5))
- consolidate StepContainer ([58d9eb7](https://github.com/continuedev/continue/commit/58d9eb7a7e8102940d59f26a65e01edb130681fd))
- consolidate toast logic into "showToast" ([ba777ed](https://github.com/continuedev/continue/commit/ba777edf4761f8bb781fce11ce45311c0b089048))
- continue sdk ([c7829bb](https://github.com/continuedev/continue/commit/c7829bbd514b962caad99a4c8c660e94f9e577b4))
- convert issue templates to issue form templates ([#507](https://github.com/continuedev/continue/issues/507)) ([fa7f2cb](https://github.com/continuedev/continue/commit/fa7f2cbdeb6013ec4bb081cb85988817f54d070c))
- **core:** add support for Llama models on Bedrock ([#1499](https://github.com/continuedev/continue/issues/1499)) ([53aab1e](https://github.com/continuedev/continue/commit/53aab1e6fd2a0f21fbca1c29b82d1f96d2f5e074))
- crawl `.mdx` docs ([d9f0c4f](https://github.com/continuedev/continue/commit/d9f0c4f131010bd4dae111b5e4290f3460d49e3a))
- create "rebuild index" dialog ([c978d7c](https://github.com/continuedev/continue/commit/c978d7c871be71cd399d02159be8c1b37a0d36ff))
- create `ApplyManager` ([652f8c4](https://github.com/continuedev/continue/commit/652f8c4c7b0879f9ab3b2cc1a25c9530de467c94))
- create docs cache ([3f8afbe](https://github.com/continuedev/continue/commit/3f8afbefeb36c389528d420ef5945c0014ef18c0))
- create file button in toolbar ([268f55b](https://github.com/continuedev/continue/commit/268f55b001e880fec92ef87df6be2bdf8fc25649))
- create markdown rules in notch ([c2571e2](https://github.com/continuedev/continue/commit/c2571e2a86e373199fe9660f95fa5b168c776035))
- create org slice ([71f8508](https://github.com/continuedev/continue/commit/71f85084f22333337a1e817e0502044771d66de4))
- create profiles slice ([9c2db6b](https://github.com/continuedev/continue/commit/9c2db6b9c7d7fd0a0164b3e2288a4135df759707))
- create RecentFilesService ([d597e6f](https://github.com/continuedev/continue/commit/d597e6fe5ea632f2bdf762ad2555c056fd736ba8))
- create stream complete reducer ([ce7d982](https://github.com/continuedev/continue/commit/ce7d98291e30658b387dd5d7de96e0f3577065ab))
- create SVG tooltip ([0cf152e](https://github.com/continuedev/continue/commit/0cf152ede652d975fd774faa40494020c7514196))
- dallin feedback pt 2 ([d158fab](https://github.com/continuedev/continue/commit/d158fab740cf803074106717560941cca8cb96c9))
- dallin's feedback ([e884853](https://github.com/continuedev/continue/commit/e88485348c8b53e9f19d1f9fc0c8b33dd5266df7))
- Dallin's feedback ([fc1ee33](https://github.com/continuedev/continue/commit/fc1ee33ed912543f596d32ae6eaacbbdbc3bcae4))
- dallins feedback ([33b1d39](https://github.com/continuedev/continue/commit/33b1d3994654f311dcae385f6ef2ef78696d1d89))
- dallins feedback ([25b5b8c](https://github.com/continuedev/continue/commit/25b5b8c50b05fe872d74b60ced4a6d8569686ed6))
- dallins feedback ([6070a64](https://github.com/continuedev/continue/commit/6070a645dd9741b433ad43fd09f37b42ec614a3e))
- disable chat-input enter-button during prompting ([e01fbe7](https://github.com/continuedev/continue/commit/e01fbe7f2cf6f53f5a611afe6df53dfc5f710e55))
- disable chat-input-editor during prompting ([7368767](https://github.com/continuedev/continue/commit/73687670da1cc7792d2c4d9ccd7b9813746a4658))
- display rules used ([53f0679](https://github.com/continuedev/continue/commit/53f06795d3910e1ec5aecdbc1fde706f68649614))
- editor change listener ([28d2c7d](https://github.com/continuedev/continue/commit/28d2c7d7e0a00c23c33ad25b8bfc3651ddafa2c5))
- **embeddings:** add gemini provider ([#1362](https://github.com/continuedev/continue/issues/1362)) ([5224572](https://github.com/continuedev/continue/commit/52245724089329c792eb79c8d512cea2f617c4a1))
- enable comment filtering ([471ca9e](https://github.com/continuedev/continue/commit/471ca9eae5e1f82e04b2d6d600631bf24876ec06))
- enable sourcemap ([2be2b73](https://github.com/continuedev/continue/commit/2be2b73f2a28834cbfef9100f479ecffc0ad13c8))
- enable WAL (Write-Ahead Logging) for improved performance and stability ([#1885](https://github.com/continuedev/continue/issues/1885)) ([e93ce84](https://github.com/continuedev/continue/commit/e93ce84cd57eacfe820c1f22668ed069020ee46e))
- enhance help center ([#1755](https://github.com/continuedev/continue/issues/1755)) ([f2a04ef](https://github.com/continuedev/continue/commit/f2a04ef3e9e49876042077f36da5f457630dcaf1))
- Enhance MCP connection refresh logic ([1a0d411](https://github.com/continuedev/continue/commit/1a0d41128f30c7f5c087c7255d6ec3292c288664))
- Ensure CancelAutocompleteAction updates on EDT ([277c20f](https://github.com/continuedev/continue/commit/277c20f3b08eaa1b0b293b06bbfce40404e5477d))
- evaluate rule ([1a1b854](https://github.com/continuedev/continue/commit/1a1b854fda4630e136d0d18b4ea69e71a00ced1e))
- explore btn ([14c9528](https://github.com/continuedev/continue/commit/14c952887399f12e739d6a0fafbdbb4d1355fe90))
- explore dialog ui ([c6cfa93](https://github.com/continuedev/continue/commit/c6cfa9334690cf7f9ecdf99959537ce522728079))
- explore dialog watcher ([741ad35](https://github.com/continuedev/continue/commit/741ad35195d6b841b459076b8dbca1057d5f6358))
- explore dialog watcher ([192a47f](https://github.com/continuedev/continue/commit/192a47f998d639f307933f33a731832ff35305ac))
- explore hub card ([5a777ce](https://github.com/continuedev/continue/commit/5a777ce464243b09d466d7a11a39ff9e45a74ec5))
- extract paths ([2ffb4ae](https://github.com/continuedev/continue/commit/2ffb4aefeb01073c924beb1c203e60c586cf96bb))
- fall back to Cheerio for headless crawling ([fd7bbce](https://github.com/continuedev/continue/commit/fd7bbce79b831a1068c5367bc4f018fe820a917a))
- fallback to chat model from apply model ([46e8dd7](https://github.com/continuedev/continue/commit/46e8dd7c38858173f375ac24f6857ed3bea2b190))
- fallback to chat model in IDE ([b601c22](https://github.com/continuedev/continue/commit/b601c22d01478d74a66bf162f3e12c4c4e1a93d7))
- filttrex ([b20b1ab](https://github.com/continuedev/continue/commit/b20b1ab5ee44d704619327a3b087b1290c634dd7))
- fix address ([d59b3b5](https://github.com/continuedev/continue/commit/d59b3b5297924b2b6343a990c09551be4af9c670))
- fix broken links ([66345e8](https://github.com/continuedev/continue/commit/66345e83550a898cd36eae67a68fb020ce957677))
- fix ctx providers w/ slash cmds ([e5608a9](https://github.com/continuedev/continue/commit/e5608a94e543cd8b66d68c222eacc82e8c021aff))
- fix filepaht issues w/ apply ([6c848ba](https://github.com/continuedev/continue/commit/6c848ba7a7fc7ea32a963653b83b5099e5ed6fad))
- fix models ([d401a5c](https://github.com/continuedev/continue/commit/d401a5c11fdf5b3267269ff3603f83318a9dd9ad))
- fix type error + formatting ([bb5e8b6](https://github.com/continuedev/continue/commit/bb5e8b641dc535fd8d60de92aba04680f7b93824))
- fix version and address ([ad0fdb9](https://github.com/continuedev/continue/commit/ad0fdb988b76bebadcbb26a3069b7f821207680f))
- get paths ([9cb5170](https://github.com/continuedev/continue/commit/9cb5170b1a23552f917265dc330ff1e05838a3de))
- get return_type and parameters from snippets ([bf426d5](https://github.com/continuedev/continue/commit/bf426d5e2147248c06ab51cab31ac5cd9e834b1d))
- glob rules ([8c5ca40](https://github.com/continuedev/continue/commit/8c5ca401d4fbda64eacf23cc0585e2c9830db345))
- go definitions ([bf65b1e](https://github.com/continuedev/continue/commit/bf65b1ebbdd3b0913d141e9f60a11414088bc78f))
- **gui:** add ability to change the session title ([1b32222](https://github.com/continuedev/continue/commit/1b32222c10c6ae6f1fd6c93f6abd8cbeab16f108))
- **gui:** Add Azure as a provider ([b7c0623](https://github.com/continuedev/continue/commit/b7c0623ac6c5067ccefd7c8486eeef2ff56d9667))
- **gui:** add flex-grow to TdDiv in history page ([#619](https://github.com/continuedev/continue/issues/619)) ([7106f42](https://github.com/continuedev/continue/commit/7106f42cb4ba7424b3dda8ed2d0e562e2fd99447))
- **gui:** improve editor highlights ([7bffa2d](https://github.com/continuedev/continue/commit/7bffa2dbeab4dde415c113bb15b71968bc51f448))
- **gui:** more config for azure provider ([e11206b](https://github.com/continuedev/continue/commit/e11206b0fa70075d21d1cc8dd4426af856ee05c2))
- handle deletions ([2c8cee9](https://github.com/continuedev/continue/commit/2c8cee9fd42fc8f18c9e6e37781e27276dae2786))
- hide empty code lines in markdown preview ([#815](https://github.com/continuedev/continue/issues/815)) ([793d022](https://github.com/continuedev/continue/commit/793d022eab292219cda75bb3255ed3d676585977))
- **history:** add sticky headers to history sections ([#621](https://github.com/continuedev/continue/issues/621)) ([3d398d0](https://github.com/continuedev/continue/commit/3d398d0b3781db8bbc51ad00f47e80b3124f5c8f))
- hover brightness on tool call div ([dba2887](https://github.com/continuedev/continue/commit/dba288748d850c7fa8047efb75e954384410a497))
- **httpContextProvider:** load AC on fetch client ([#1150](https://github.com/continuedev/continue/issues/1150)) ([638f192](https://github.com/continuedev/continue/commit/638f1922590d7bd46d6e4d46a4d25bf1b33b26fa))
- if rules ([6224af6](https://github.com/continuedev/continue/commit/6224af63d36e6b96b068c6d4230ee2315bd94d43))
- if rules ([d0e922d](https://github.com/continuedev/continue/commit/d0e922dad4947ac8af4e9d0c76921da56cdc2953))
- if-rule ([211fcb7](https://github.com/continuedev/continue/commit/211fcb77407cd261f2bb26198667388ea6270a05))
- if-rule ([9baa4e9](https://github.com/continuedev/continue/commit/9baa4e93be7f9c2fe057e453237eaa9d1aaa8ff0))
- if-rules ([06b76d7](https://github.com/continuedev/continue/commit/06b76d752fb4f83b14053fffe2f85cb819e39ae6))
- if-rules ([0a13a46](https://github.com/continuedev/continue/commit/0a13a4604d7607793bea66b9cea3e89353bf3af4))
- if-rules ([ccdc1b9](https://github.com/continuedev/continue/commit/ccdc1b953e22ca4aa26e32b9e80bc31ea30f274c))
- ignore node_modules in js and ts code definitions ([3c9071a](https://github.com/continuedev/continue/commit/3c9071a09b114a019a593a7f241975f419cc4e43))
- impl /multifile-edit ([f88f28b](https://github.com/continuedev/continue/commit/f88f28b8551e95e4eb346fafa68d6223f9017d5a))
- improve chat thread ui for better readability ([#1786](https://github.com/continuedev/continue/issues/1786)) ([8478af6](https://github.com/continuedev/continue/commit/8478af63c18b0c35d59d2326ca4e8687a42a624b))
- improve chunking desc on large projects ([3844208](https://github.com/continuedev/continue/commit/3844208a3b9b3daaff085daad8ab933f27fcb5f3))
- improve dropshadow on jb inline edit ([35b94e7](https://github.com/continuedev/continue/commit/35b94e710260782d0311ee22a2849203b8965e38))
- improve dropshadow on jb inline edit ([75e2d2f](https://github.com/continuedev/continue/commit/75e2d2f39df30424b3831615ee3720a2844a60a5))
- improve fatal error message ([1d77ad3](https://github.com/continuedev/continue/commit/1d77ad31949042c7702b59f83a82ca51dcce4900))
- improve input and tooltip ux ([#1923](https://github.com/continuedev/continue/issues/1923)) ([2c13776](https://github.com/continuedev/continue/commit/2c13776c06cfe9c775777bf2fffdcf7dfa2169ee))
- improve model retrieval logic in InlineEditAction ([52b1704](https://github.com/continuedev/continue/commit/52b1704f3c0fe991b32959d04c660fe55e899b2c))
- improve prompt log formatting + detail ([2376daf](https://github.com/continuedev/continue/commit/2376dafce325c87d87090e7cf995beffcb98d3b4))
- Improve ProtocolClient initialization and GUI loading ([0c829fd](https://github.com/continuedev/continue/commit/0c829fd99d0e898c1afdc90a0957c907dcb7f4e2))
- improve settings tabs ([a5f974b](https://github.com/continuedev/continue/commit/a5f974bf1602dabe3939ddc0ac7b5a05ac8e716b))
- improve StreamError dialog ([ecb63a0](https://github.com/continuedev/continue/commit/ecb63a0222c7ae5e051bea02d98a4a2de7c1e2e5))
- improve styling on code to edit ([47fbe4f](https://github.com/continuedev/continue/commit/47fbe4f9b81e1ef31d60f1110d2bf240fa6e7d40))
- include recently + open files in codebase search ([#1833](https://github.com/continuedev/continue/issues/1833)) ([3e0fae3](https://github.com/continuedev/continue/commit/3e0fae35a75bb8dc117bc3eb008eee122e0e12ae))
- init profiles prefs ([d6ad3e2](https://github.com/continuedev/continue/commit/d6ad3e2071ca1ee3f0b5cd4d8e93852a84fd138a))
- insert prompt into input on click ([a73123e](https://github.com/continuedev/continue/commit/a73123e35ce29f1776b5afb456c9c67dc9495099))
- instant apply check for diff rejectection ([98cb185](https://github.com/continuedev/continue/commit/98cb185c926d49a6f5af00f8b53e207743da815a))
- integrate Moonshot AI model provider and update UI translations ([7ffb95e](https://github.com/continuedev/continue/commit/7ffb95e9d0fc964b55b3f92149515fa79fc3aa0d))
- items used text ([#1973](https://github.com/continuedev/continue/issues/1973)) ([28a2042](https://github.com/continuedev/continue/commit/28a2042b7f604889cd0c7c76ee3ec0791a00e3ba))
- **jb:** add ide logs ([22d84ae](https://github.com/continuedev/continue/commit/22d84ae726c40d65449c39b2ece4101f32608870))
- **jb:** add plugin actions ([629641a](https://github.com/continuedev/continue/commit/629641a4adf2a1e2fe1c9a69fd431718b34c4263))
- **jb:** create per-IDE tutorial files ([e3b5cbf](https://github.com/continuedev/continue/commit/e3b5cbfda453c727c6f81d04aa8bed1b9efe8301))
- **jb:** fix meta key bugs in tiptap ([7682c71](https://github.com/continuedev/continue/commit/7682c71575c7ffc03d625510187d792e9d971227))
- **jb:** impl "apply" button ([802cb43](https://github.com/continuedev/continue/commit/802cb43cd6b6abf8613591eb38c445e6bd4daa7d))
- **JB:** impl `showFile` ([24bb5eb](https://github.com/continuedev/continue/commit/24bb5eb0dab8467f606d249f8be16fcb9ff083c0))
- **JB:** scroll to top of file on full file edit ([c36a1ae](https://github.com/continuedev/continue/commit/c36a1ae62851383978080bc5674e7ba4ca0e9cc1))
- jetbrains tutorial explore hub ([054b095](https://github.com/continuedev/continue/commit/054b095dab51b3abc7a3d83587e0b5f67b8acdf6))
- make [@codebase](https://github.com/codebase) a hardcoded ctx provider ([#1818](https://github.com/continuedev/continue/issues/1818)) ([7b86678](https://github.com/continuedev/continue/commit/7b866787d947106b77ac0cef92b75ba36e09e7fc))
- make `assistant` optional on sdk ([48429ae](https://github.com/continuedev/continue/commit/48429ae04f957a98c24d841316c722b91841c329))
- make deletion line highlight wider ([10a5ec1](https://github.com/continuedev/continue/commit/10a5ec18a9df189d58c60886be296f1107a1dfc2))
- make disabled state a tooltip ([#1653](https://github.com/continuedev/continue/issues/1653)) ([6cf0102](https://github.com/continuedev/continue/commit/6cf0102875316a89752d17d4fe08e4b21fd2e603))
- manual ripgrep downloads for JB ([587459c](https://github.com/continuedev/continue/commit/587459c007ba130a4fd6f5ffde246dd8625486a8))
- markdown rules ([a21d350](https://github.com/continuedev/continue/commit/a21d350ea14a98cf16904ae678de64a83775da7c))
- merge conflicts ([82a328c](https://github.com/continuedev/continue/commit/82a328ca15306e0b902075c0661cd1b2bb8ed9a8))
- model name update ([045c6bd](https://github.com/continuedev/continue/commit/045c6bdfec45ffadb25b3db2306b78f34780e84b))
- modify vitest command, add test coverage feature ([96c70d0](https://github.com/continuedev/continue/commit/96c70d07d283130176cbda68afdb02ddc6b20366))
- more cleanup ([6c294e7](https://github.com/continuedev/continue/commit/6c294e76834561848305ac8fb6bcb707ea21b172))
- more cleanup ([bf7cf66](https://github.com/continuedev/continue/commit/bf7cf661cc4357029492f067ca57b1a43401aaaa))
- more dynamic imports for LanceDB ([90c6631](https://github.com/continuedev/continue/commit/90c663146edc965bbe0b87b737755742315a8fa1))
- more moving around ([b0492dd](https://github.com/continuedev/continue/commit/b0492ddfee2e6050d833d4ad07a942dbdd830c55))
- more styling updates ([c7b3ea8](https://github.com/continuedev/continue/commit/c7b3ea890ac2bade08424e7a23301cea319aea35))
- more visible assistant refresh + submenus ([1fd2ce9](https://github.com/continuedev/continue/commit/1fd2ce90f22c0dd6ebb9ec5b2542f85475844436))
- move .prompts into slash cmd ([1d1f705](https://github.com/continuedev/continue/commit/1d1f705f7dfb1d517b90e03b82856e625f3f323e))
- move `StepContainerPreToolbar` ([979bcb9](https://github.com/continuedev/continue/commit/979bcb97ec88d91d0ed0bd14fd5b6b7dc4c670ae))
- move apply accept/reject into lump ([071ecb9](https://github.com/continuedev/continue/commit/071ecb91eb9c627e96439b7a5be260ec446500b0))
- move apply manager instantiation ([2413d24](https://github.com/continuedev/continue/commit/2413d241c6448f65c99d9286e72a01bc8acc429a))
- move Edit into Chat page ([731b54e](https://github.com/continuedev/continue/commit/731b54eb30bd7184e42c397db658020b4a70d5f7))
- move error indicator into lump ([1f1d4f4](https://github.com/continuedev/continue/commit/1f1d4f4a1c5b365258db219e2a0ae8eecf3656a8))
- move free trial out of assistant ([2c58a33](https://github.com/continuedev/continue/commit/2c58a33a6a937ea7beb1aa017bce64ec4cfe32d0))
- move rule parsing to `config-yaml` ([f2d7290](https://github.com/continuedev/continue/commit/f2d72902cb45c165ca73483dd5c0a90ac89bcc49))
- move SymbolLink ([e3bbd6b](https://github.com/continuedev/continue/commit/e3bbd6b7cfa04183f7a60470a55e4a2618083a21))
- move vLLM rerank response in the VLLM.ts and remove unused types ([c8d9d95](https://github.com/continuedev/continue/commit/c8d9d959f10b34cb0af857cfa178c1f9df30f5cf))
- onboarding card upgrade tab ([41bc0e9](https://github.com/continuedev/continue/commit/41bc0e922b333ee404ddb17e5bdcf7c83b7a6ca0))
- only autoscroll Continue console when focused on last element ([40390f5](https://github.com/continuedev/continue/commit/40390f583bea74766c3eff48cb8ceda555112780))
- open lump on submit onboarding ([f77f662](https://github.com/continuedev/continue/commit/f77f662c6e7313db645656e003c9d0145646388e))
- org select ([dc751a3](https://github.com/continuedev/continue/commit/dc751a3274948cb12b47fb7b57106a8349e26870))
- php definitions ([8da703f](https://github.com/continuedev/continue/commit/8da703f74525213dc212efa6b763d4be06dee38b))
- php definitions ([eadc96e](https://github.com/continuedev/continue/commit/eadc96ebef6653a996dff32095fd87a86703058d))
- poll in JB after upgrading ([89d4b32](https://github.com/continuedev/continue/commit/89d4b32bba3bede3ac9f001c6c013ec0bb5a9ca0))
- postgres context provider first slice ([758a81c](https://github.com/continuedev/continue/commit/758a81c7373f398294ccb80d00a5c7d181f151d7))
- pr_checks update ([1cc81d4](https://github.com/continuedev/continue/commit/1cc81d4b7e6ed6aedcc19753eea623acbfc8f8c1))
- preserve edit when rejecting ([32bf80d](https://github.com/continuedev/continue/commit/32bf80d7d280fe815b547475b4a75fce92ba990d))
- profiles slice ([b82d29c](https://github.com/continuedev/continue/commit/b82d29c2df154a632c89d32c6847f5bf43b6564b))
- promote reject diff action ([16cbe94](https://github.com/continuedev/continue/commit/16cbe9499431d7eec64d0695821a223302d980cb))
- prompt blocks ([9df325a](https://github.com/continuedev/continue/commit/9df325a71f3340c46820ac5335249095cda443ac))
- Provide workspace path to HttpContextProvider ([4873f58](https://github.com/continuedev/continue/commit/4873f5805f89bc1f443211515dd7afb3fbc2cfff))
- python context ([42d5f66](https://github.com/continuedev/continue/commit/42d5f660727dcf315d12193f70d23b283e7af10d))
- recursively apply quick actions codelens ([#1925](https://github.com/continuedev/continue/issues/1925)) ([d5155da](https://github.com/continuedev/continue/commit/d5155dac697e5fa8b4241ebebe48bbfa66dc2c55))
- redo action ([121be85](https://github.com/continuedev/continue/commit/121be85a69837a1d31dde31f7fca7098458ed38d))
- refactor onboarding card ([b86bc6f](https://github.com/continuedev/continue/commit/b86bc6f727e292c2d3a8fb1511646c36e2a2531f))
- reintroduce lazy apply for full files ([6504bd5](https://github.com/continuedev/continue/commit/6504bd55659823c8117d04ddb447f91d9932d086))
- remove `models` property ([7f8882e](https://github.com/continuedev/continue/commit/7f8882eee136b90db9400175f9ee00fa1aa3ad93))
- remove defaultTitle ([1bf359a](https://github.com/continuedev/continue/commit/1bf359a55fdb833569878167a1440e66c65d7249))
- remove edit as a mode ([4f371a0](https://github.com/continuedev/continue/commit/4f371a0eb654067c626a41eb53718f565873975b))
- remove tools from schema ([5bbda4f](https://github.com/continuedev/continue/commit/5bbda4f115a208ebd2df0c63ede75bfc5e131b54))
- remove useFileExists ([2b34ee8](https://github.com/continuedev/continue/commit/2b34ee8c4156c544a3abd9616080f4d8e0b565a1))
- rename to `Open Assistant configuration` ([9d8139a](https://github.com/continuedev/continue/commit/9d8139a6fae992eb1cb77248cc297d0a95683c12))
- replace logo and deepseek model base info ([7749349](https://github.com/continuedev/continue/commit/7749349f8996f5da186ba1a8b2910443121ea2fc))
- restructure for easier module publishing ([b51905d](https://github.com/continuedev/continue/commit/b51905da95307ed17f3fa7a466e94514bcd4113a))
- retrieve AWS credentials from Env and from ECS/EC2 instance ([53426a1](https://github.com/continuedev/continue/commit/53426a12b858f341c9f70f8ee3513dbea39d648d))
- reusable card ([d11376b](https://github.com/continuedev/continue/commit/d11376ba7f2b7ab1a85c75f361631fc2c5c26ab5))
- rule colocation ([39dc367](https://github.com/continuedev/continue/commit/39dc367371c0df605f663c76fe490c4736f00153))
- rule glob ([f95cdfa](https://github.com/continuedev/continue/commit/f95cdfaf83569159cd7aed02134be7dd5f4029cf))
- rules ([2620064](https://github.com/continuedev/continue/commit/2620064c53ecf7de3d61b34043d2c0450f68b13c))
- rules display ([40914a8](https://github.com/continuedev/continue/commit/40914a8bf3b4f2c1764873bc3172feab3bf23f3a))
- rules policies ([8a2ffb3](https://github.com/continuedev/continue/commit/8a2ffb3a859d4fbec3299500a0d860c0f725573d))
- rules preview ([d57d35d](https://github.com/continuedev/continue/commit/d57d35d5bdf6015593283a2aecbf88bca1c8c574))
- run prettier ([ecdb4f5](https://github.com/continuedev/continue/commit/ecdb4f530dee01e8a67e7fd964206428d379b4db))
- **scaleway:** update supported models ([8b04ba6](https://github.com/continuedev/continue/commit/8b04ba6b233d72f1e61ea67fd63de51d03629282))
- separate toolbar action buttons ([aba6e4e](https://github.com/continuedev/continue/commit/aba6e4e61c6c7b548126a79ffeb0608e1de1147b))
- show disableIndexing in More ([5d95b62](https://github.com/continuedev/continue/commit/5d95b62e683c0ea50eb519f7cc2851736e8ee64b))
- show num diffs in toolbar ([ad6c0d0](https://github.com/continuedev/continue/commit/ad6c0d0bb850308993e53c4117cc45664fd8970e))
- simplify types ([b18fd4a](https://github.com/continuedev/continue/commit/b18fd4aee0c66e41113bce4de1fa526da1dc16c6))
- simplify typings ([c6fa6d7](https://github.com/continuedev/continue/commit/c6fa6d78a714ba6bba44ec2ef9825080b0a5b69a))
- single default quick pick to edit ([#1743](https://github.com/continuedev/continue/issues/1743)) ([ca7bde9](https://github.com/continuedev/continue/commit/ca7bde9b5e10d684ea44291c67eb294edc357240))
- skip hub onboarding in free trial ([d995149](https://github.com/continuedev/continue/commit/d99514995588cc2c502df99757e22f5ac3e2c7a1))
- skip onboarding subtext ([9e6ff4b](https://github.com/continuedev/continue/commit/9e6ff4b91447b03d1b9949499cf5135d7a369df9))
- smaller headings, use assistant name ([85804ff](https://github.com/continuedev/continue/commit/85804ff9e54adf30f1a4b23b3003b39338a82065))
- split diff ([b7defc8](https://github.com/continuedev/continue/commit/b7defc8d912b09d9323e05a82442bea17c6faef4))
- split diffs ([3c537f5](https://github.com/continuedev/continue/commit/3c537f51c13ee9064a3e17f3aec62dc5283ae3e2))
- split diffs ([39087c2](https://github.com/continuedev/continue/commit/39087c2207e0dd3764761e91b9a2abd3ca6db906))
- support o3/o4 as agents ([8bb18fd](https://github.com/continuedev/continue/commit/8bb18fd2ee4a0fb81b2f24903e47fb1b0beade36))
- supports agent 4 deepseek ([82d8e1e](https://github.com/continuedev/continue/commit/82d8e1e6795e7258880d414c4bb78c6f83b17883))
- toolbar header for all codeblocks ([635f7cb](https://github.com/continuedev/continue/commit/635f7cb168f264446e40c6887347327b101ee413))
- tutorial listener ([6e01c7d](https://github.com/continuedev/continue/commit/6e01c7d0ca0d139e471edf40018d35e1033638f3))
- unskip tests ([a5619e2](https://github.com/continuedev/continue/commit/a5619e29c8a0230f578c092c028c7cc6f3066afb))
- update azure uri for foundry users ([638969b](https://github.com/continuedev/continue/commit/638969b7c7594f36bfefd59a986032ba597f88fa))
- update btn colors ([b7c7171](https://github.com/continuedev/continue/commit/b7c71719e55e728f944399789f6749e493d5d13e))
- update docs and input labelling ([1847317](https://github.com/continuedev/continue/commit/18473174365c88e7e83fd8123cb6af9318e10462))
- update e2e tests ([93cec27](https://github.com/continuedev/continue/commit/93cec27e28773778e3223424fb72026c9e3ead44))
- update executable perms on linux/macos ([dcbd80e](https://github.com/continuedev/continue/commit/dcbd80ecf47adeb49b9484bb1e1b3c43ff80bff8))
- update onboarding w/ embeddings model ([#1570](https://github.com/continuedev/continue/issues/1570)) ([ed56c8f](https://github.com/continuedev/continue/commit/ed56c8f7f325f19c9b4d1c7e3cb775f850beaea9))
- update PreToolbar ([3933791](https://github.com/continuedev/continue/commit/39337910f125a71414cf2c39b96250fc512777be))
- update redux store schemas ([438cba4](https://github.com/continuedev/continue/commit/438cba4501026ef1705987b23f419570d1175ea1))
- update styling ([ef349ec](https://github.com/continuedev/continue/commit/ef349ecec90dac3d15eb80ae17cf24c155d1e683))
- update sys prompt ([25c7144](https://github.com/continuedev/continue/commit/25c714494588433d1c43d6af99cce175b48e21cc))
- update to work as normal context provider ([0ace169](https://github.com/continuedev/continue/commit/0ace1699356be8024a8ab49ccadc4b1b22c18e08))
- update tutorial files w/ agent mode step ([8eaf078](https://github.com/continuedev/continue/commit/8eaf078638c0d2d9c14aa0cc8e465df66f01e4df))
- update URL replace logic ([fe6c9a1](https://github.com/continuedev/continue/commit/fe6c9a1477ab814f3cc4d0973e851701621d2c54))
- updated prompt docs ([204aa51](https://github.com/continuedev/continue/commit/204aa51a944906fe629e92095a3f7f86113b9f6c))
- use @lancedb/vectordb-win32-arm64-msvc ([0277320](https://github.com/continuedev/continue/commit/027732073aa0fb5202b558f4c0d71c71c6b8acbb))
- use `fetch` instead of `http` ([401d67b](https://github.com/continuedev/continue/commit/401d67bbd0dc11fd6eda2f7d9c17dfd4da0a43f5))
- use `instant` property on diff manager ([2f12bbd](https://github.com/continuedev/continue/commit/2f12bbdf25b1ff189a88d37bfba4f3bfe7f2f3bc))
- use bm25 for fts ([2e5b579](https://github.com/continuedev/continue/commit/2e5b5794e9b394405c127893100be21e008b45ad))
- use clipboard content ([9dd6284](https://github.com/continuedev/continue/commit/9dd6284b8e4b1dc09b704bc00d5231af9569b6bc))
- use correct deployment for azure ([8e24fcf](https://github.com/continuedev/continue/commit/8e24fcf4a0ee655dfbe83d95b82855041f0ba246))
- use crawlee for docs service ([a51c520](https://github.com/continuedev/continue/commit/a51c520deefa49f9d67aaa2469bb261af00dab80))
- use exponential backoff in llm chat ([#1115](https://github.com/continuedev/continue/issues/1115)) ([a87df40](https://github.com/continuedev/continue/commit/a87df40a2c875a1538aa112d7d35c03137990ee2))
- use git diff, improve comment formatting ([1adbd9a](https://github.com/continuedev/continue/commit/1adbd9a456f6c4c2f56b78d6bd5e3b8737f60ec5))
- use hub blocks for local onboarding ([1b45308](https://github.com/continuedev/continue/commit/1b453083b14e60afda14de69b7bfb2f7ce74194b))
- use meyers diff after initial edit/apply ([f5e7f9c](https://github.com/continuedev/continue/commit/f5e7f9ce71d4c34e8ee72b0d78fe89c68d0b7170))
- use s3 for global docs cache on all docs ([0156409](https://github.com/continuedev/continue/commit/0156409dac5a445b055ede921c1c07706759ff4e))
- use theme color for shortcuts rows ([44fdd65](https://github.com/continuedev/continue/commit/44fdd65967b561527865a02cafce0de4cdc9fa70))
- use thunj ([eae1dd2](https://github.com/continuedev/continue/commit/eae1dd20bb296099db94175c43bf7af445e97200))
- v1 onboarding card ([0d47647](https://github.com/continuedev/continue/commit/0d476473bda2ef8bea78064c89c5bc1f6818fec1))
- vitest ([3cd19f5](https://github.com/continuedev/continue/commit/3cd19f5fabdb6eab3be381ccef8de743b37fcf29))
- **VSC:** give option to disable quick fix ([951784c](https://github.com/continuedev/continue/commit/951784c790664a80d87b54e33886a3582137ad50))
- vscode config for json ([666fb18](https://github.com/continuedev/continue/commit/666fb186d6b4cee9dcc95e1adc6b42748300de4c))
- withExponentialBackoff utility function ([ad8991e](https://github.com/continuedev/continue/commit/ad8991eac0f7f40f3877d56392592d8d83e173a6))
- working `copy-client` ([09beb58](https://github.com/continuedev/continue/commit/09beb58260f32043c89cff39c6e8567f25099c9c))
- write initial unit test for apply ([f4a59dc](https://github.com/continuedev/continue/commit/f4a59dc185c3ea7ceb29a4629ea0c7dc82f44dbb))

### Performance Improvements

- :green_heart: hardcode distro paths ([edf0f56](https://github.com/continuedev/continue/commit/edf0f5603730d01d7ca4ca055dd49da596648627))
- :zap: don't show server loading immediately ([5047dfc](https://github.com/continuedev/continue/commit/5047dfcd2a2e47468c15ed05c69781cc615ef723))
- **llm:** Optimize pruneLines functions in countTokens ([35b3189](https://github.com/continuedev/continue/commit/35b3189538da5891617a79ba9c0badb0bb7dc1bc))
- **llm:** Optimize pruneLines functions in countTokens ([28cdd1c](https://github.com/continuedev/continue/commit/28cdd1cee25973aa9a8a5cfdcbcc436e0b7f6240))
- **llm:** Optimize pruneLines functions in countTokens ([881f8b3](https://github.com/continuedev/continue/commit/881f8b3139794aca0a6699c3d34ebf8fba01b789))

### Reverts

- Revert "Add citations to the log" ([caf7288](https://github.com/continuedev/continue/commit/caf7288d36c1361b6cb279821d9ee377636f72f7))
- Revert "feat: add bookmark logic" ([730ac56](https://github.com/continuedev/continue/commit/730ac56d5a15a6b2931330aeb849e2d260d2644a))
- Revert "update TabBar to use Redux for session management" ([9f13b92](https://github.com/continuedev/continue/commit/9f13b92b3fbc4157124ccf120028312390eed70f))
- :bookmark: update version ([d6ebc6d](https://github.com/continuedev/continue/commit/d6ebc6d969ccafc753ecf00e0309777b96b4ad11))
- :bug: revert unecessary changes from yesterday ([3629ddd](https://github.com/continuedev/continue/commit/3629dddb1daea23fbd29b03705e742ec2a22d6ec))
- :fire: disable fallback_context_item ([a572db4](https://github.com/continuedev/continue/commit/a572db40b6c9ce98b07e89d34f8652e19f91187e))
