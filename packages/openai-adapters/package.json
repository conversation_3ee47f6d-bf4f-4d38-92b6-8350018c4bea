{"name": "@continuedev/openai-adapters", "version": "1.1.1", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"test": "vitest", "build": "tsc"}, "author": "<PERSON> and <PERSON>", "license": "Apache-2.0", "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.842.0", "@aws-sdk/credential-providers": "^3.840.0", "@continuedev/config-types": "file:../config-types", "@continuedev/config-yaml": "file:../config-yaml", "@continuedev/fetch": "file:../fetch", "dotenv": "^16.5.0", "google-auth-library": "^10.1.0", "json-schema": "^0.4.0", "node-fetch": "^3.3.2", "openai": "^4.104.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.3", "@semantic-release/npm": "^12.0.2", "@semantic-release/release-notes-generator": "^14.0.3", "@types/follow-redirects": "^1.14.4", "@types/jest": "^29.5.14", "@types/json-schema": "^7.0.15", "cross-env": "^7.0.3", "semantic-release": "^24.2.7", "ts-jest": "^29.2.3", "ts-node": "^10.9.2", "vitest": "^3.2.4"}}