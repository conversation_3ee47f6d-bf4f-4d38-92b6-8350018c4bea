/**
 * HTTP/2 based fetch implementation using Node.js native http2 module
 * This replaces undici-fetch.js to use native HTTP/2 support
 * Maintains compatibility with existing API while leveraging HTTP/2 performance benefits
 */

import http2 from "node:http2";
import https from "node:https";
import http from "node:http";
import { <PERSON>uffer } from "node:buffer";
import { Readable } from "node:stream";
import { URL } from "node:url";

const {
  HTTP2_HEADER_METHOD,
  HTTP2_HEADER_PATH,
  HTTP2_HEADER_STATUS,
  HTTP2_HEADER_CONTENT_LENGTH,
  HTTP2_METHOD_GET,
} = http2.constants;

/**
 * Response implementation compatible with fetch API
 */
class Http2Response {
  constructor(status, statusText, headers, body, url) {
    this.status = status;
    this.statusText = statusText;
    this.headers = new Map();
    this.body = body;
    this.url = url;
    this.ok = status >= 200 && status < 300;
    this.redirected = false;
    this.type = "basic";
    this.bodyUsed = false;

    // Process headers
    if (headers) {
      for (const [key, value] of Object.entries(headers)) {
        if (key.startsWith(":")) continue; // Skip HTTP/2 pseudo headers
        this.headers.set(
          key.toLowerCase(),
          Array.isArray(value) ? value.join(", ") : String(value),
        );
      }
    }

    // Add forEach method to headers
    this.headers.forEach = function (callback, thisArg) {
      for (const [key, value] of this.entries()) {
        callback.call(thisArg, value, key, this);
      }
    };

    // Add get method to headers
    this.headers.get = function (name) {
      return Map.prototype.get.call(this, name.toLowerCase());
    };
  }

  async text() {
    if (this.bodyUsed) {
      throw new TypeError("Body has already been consumed");
    }
    this.bodyUsed = true;

    if (!this.body) {
      return "";
    }

    if (typeof this.body === "string") {
      return this.body;
    }

    if (Buffer.isBuffer(this.body)) {
      return this.body.toString("utf8");
    }

    if (this.body instanceof Readable) {
      const chunks = [];
      for await (const chunk of this.body) {
        chunks.push(chunk);
      }
      return Buffer.concat(chunks).toString("utf8");
    }

    return String(this.body);
  }

  async json() {
    const text = await this.text();
    return JSON.parse(text);
  }

  async arrayBuffer() {
    if (this.bodyUsed) {
      throw new TypeError("Body has already been consumed");
    }
    this.bodyUsed = true;

    if (!this.body) {
      return new ArrayBuffer(0);
    }

    if (Buffer.isBuffer(this.body)) {
      return this.body.buffer.slice(
        this.body.byteOffset,
        this.body.byteOffset + this.body.byteLength,
      );
    }

    if (this.body instanceof Readable) {
      const chunks = [];
      for await (const chunk of this.body) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);
      return buffer.buffer.slice(
        buffer.byteOffset,
        buffer.byteOffset + buffer.byteLength,
      );
    }

    const text = String(this.body);
    return Buffer.from(text, "utf8").buffer;
  }

  clone() {
    if (this.bodyUsed) {
      throw new TypeError("Body has already been consumed");
    }
    return new Http2Response(
      this.status,
      this.statusText,
      Object.fromEntries(this.headers),
      this.body,
      this.url,
    );
  }
}

/**
 * Create HTTP/2 session with connection pooling
 */
const sessionCache = new Map();

function getSession(origin, options = {}) {
  const key = `${origin}:${JSON.stringify(options)}`;

  if (sessionCache.has(key)) {
    const session = sessionCache.get(key);
    if (!session.closed && !session.destroyed) {
      return session;
    }
    sessionCache.delete(key);
  }

  console.debug(`create new session for ${origin}`);
  const session = http2.connect(origin, {
    // Connection options
    maxSessionMemory: 10,
    maxHeaderListPairs: 2000,
    maxOutstandingPings: 10,
    maxSendHeaderBlockLength: 65536,
    paddingStrategy: http2.constants.PADDING_STRATEGY_NONE,
    peerMaxConcurrentStreams: 100,

    // TLS options from agent
    ...(options.ca && { ca: options.ca }),
    ...(options.cert && { cert: options.cert }),
    ...(options.key && { key: options.key }),
    ...(options.rejectUnauthorized !== undefined && {
      rejectUnauthorized: options.rejectUnauthorized,
    }),

    // Timeout settings
    timeout: options.timeout || 10000,
  });

  // Handle session errors
  session.on("error", (err) => {
    console.debug(`[HTTP2-FETCH] Session error: ${err.message}`);
    sessionCache.delete(key);
  });

  // Clean up closed sessions
  session.on("close", () => {
    sessionCache.delete(key);
  });

  // Auto-cleanup after timeout
  session.setTimeout(options.sessionTimeout || 300000, () => {
    session.close();
  });

  sessionCache.set(key, session);
  return session;
}

/**
 * Fallback to HTTP/1.1 for non-HTTPS or when HTTP/2 fails
 */
async function fallbackToHttp1(url, options) {
  return new Promise((resolve, reject) => {
    // Check if request is already aborted
    if (options.signal?.aborted) {
      resolve(new Http2Response(499, "Client Closed Request", {}, null, url));
      return;
    }

    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === "https:";
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
      agent: options.agent,
    };

    // Add SSL options for HTTPS
    if (isHttps && options.agent && options.agent.options) {
      const agentOptions = options.agent.options;
      Object.assign(requestOptions, {
        ca: agentOptions.ca,
        cert: agentOptions.cert,
        key: agentOptions.key,
        rejectUnauthorized: agentOptions.rejectUnauthorized,
      });
    }

    const req = client.request(requestOptions, (res) => {
      const chunks = [];

      res.on("data", (chunk) => {
        chunks.push(chunk);
      });

      res.on("end", () => {
        const body = Buffer.concat(chunks);
        const response = new Http2Response(
          res.statusCode,
          res.statusMessage || "",
          res.headers,
          body,
          url,
        );
        resolve(response);
      });
    });

    // Handle abort signal for HTTP/1.1 requests
    let abortListener;
    if (options.signal) {
      abortListener = () => {
        console.debug(`[HTTP2-FETCH] HTTP/1.1 request aborted for URL: ${url}`);
        req.destroy();
        resolve(new Http2Response(499, "Client Closed Request", {}, null, url));
      };
      options.signal.addEventListener("abort", abortListener);
    }

    req.on("error", (err) => {
      if (abortListener && options.signal) {
        options.signal.removeEventListener("abort", abortListener);
      }
      // Handle abort-related errors
      if (
        err.code === "ABORT_ERR" ||
        err.name === "AbortError" ||
        options.signal?.aborted
      ) {
        resolve(new Http2Response(499, "Client Closed Request", {}, null, url));
        return;
      }
      reject(err);
    });

    req.on("timeout", () => {
      if (abortListener && options.signal) {
        options.signal.removeEventListener("abort", abortListener);
      }
      req.destroy();
      reject(new Error("Request timeout"));
    });

    // Send body if present
    if (options.body) {
      if (typeof options.body === "string") {
        req.write(options.body);
      } else if (Buffer.isBuffer(options.body)) {
        req.write(options.body);
      } else if (options.body instanceof Readable) {
        options.body.pipe(req);
        return;
      }
    }

    req.end();
  });
}

/**
 * HTTP/2 fetch implementation
 * @param {string | URL} url - Request URL
 * @param {Object} options - Fetch options with agent, proxy support
 * @returns {Promise<Http2Response>} - Response promise
 */
export default async function fetch(url, options = {}) {
  const startTime = Date.now();
  const urlObj = new URL(url);
  const shouldLog =
    typeof url === "string"
      ? url.endsWith("completions")
      : urlObj.pathname?.endsWith("completions");

  if (shouldLog) {
    console.debug(
      `[HTTP2-FETCH] Request started: ${startTime}ms - URL: ${url}`,
    );
  }

  // Check if request is already aborted
  if (options.signal?.aborted) {
    return new Http2Response(499, "Client Closed Request", {}, null, url);
  }

  try {
    // Only use HTTP/2 for HTTPS URLs
    if (urlObj.protocol !== "https:") {
      if (shouldLog) {
        console.debug(
          "[HTTP2-FETCH] Using HTTP/1.1 fallback for non-HTTPS URL",
        );
      }
      return await fallbackToHttp1(url, options);
    }

    // Extract agent options
    const agentOptions = options.agent?.options || {};
    const origin = `${urlObj.protocol}//${urlObj.host}`;

    // Get or create HTTP/2 session
    const session = getSession(urlObj, agentOptions);

    // Prepare headers
    const headers = {
      [HTTP2_HEADER_METHOD]: options.method || HTTP2_METHOD_GET,
      [HTTP2_HEADER_PATH]: urlObj.pathname + urlObj.search,
      ...options.headers,
    };

    // Add content-length for body requests
    if (options.body) {
      if (typeof options.body === "string") {
        headers[HTTP2_HEADER_CONTENT_LENGTH] = Buffer.byteLength(options.body);
      } else if (Buffer.isBuffer(options.body)) {
        headers[HTTP2_HEADER_CONTENT_LENGTH] = options.body.length;
      }
    }

    // Create request stream
    const req = session.request(headers);

    // Set timeout
    req.setTimeout(options.timeout || 30000, () => {
      req.close(http2.constants.NGHTTP2_CANCEL);
    });

    // Handle abort signal
    let abortListener;
    if (options.signal) {
      abortListener = () => {
        if (shouldLog) {
          console.debug(`[HTTP2-FETCH] Request aborted for URL: ${url}`);
        }
        req.close(http2.constants.NGHTTP2_CANCEL);
      };
      options.signal.addEventListener("abort", abortListener);
    }

    // Handle the response
    const response = await new Promise((resolve, reject) => {
      let responseHeaders = {};
      let statusCode = 200;
      const chunks = [];
      let isResolved = false;

      const cleanup = () => {
        if (abortListener && options.signal) {
          options.signal.removeEventListener("abort", abortListener);
        }
      };

      req.on("response", (headers) => {
        responseHeaders = headers;
        statusCode = headers[HTTP2_HEADER_STATUS];
      });

      req.on("data", (chunk) => {
        chunks.push(chunk);
      });

      req.on("end", () => {
        if (isResolved) return;
        isResolved = true;
        cleanup();

        const body = Buffer.concat(chunks);
        const response = new Http2Response(
          statusCode,
          getStatusText(statusCode),
          responseHeaders,
          body,
          url,
        );
        resolve(response);
      });

      req.on("error", (err) => {
        if (isResolved) return;
        isResolved = true;
        cleanup();

        // Handle abort-related errors
        if (
          err.code === "ABORT_ERR" ||
          err.name === "AbortError" ||
          (options.signal?.aborted && err.code === "ERR_HTTP2_STREAM_CANCEL")
        ) {
          const abortResponse = new Http2Response(
            499,
            "Client Closed Request",
            {},
            null,
            url,
          );
          resolve(abortResponse);
          return;
        }

        // Try HTTP/1.1 fallback on HTTP/2 errors
        if (
          err.code === "ERR_HTTP2_ERROR" ||
          err.code === "ERR_HTTP2_PROTOCOL_ERROR"
        ) {
          if (shouldLog) {
            console.debug(
              `[HTTP2-FETCH] HTTP/2 error, falling back to HTTP/1.1: ${err.message}`,
            );
          }
          fallbackToHttp1(url, options).then(resolve).catch(reject);
        } else {
          reject(err);
        }
      });

      req.on("timeout", () => {
        if (isResolved) return;
        isResolved = true;
        cleanup();

        req.close(http2.constants.NGHTTP2_CANCEL);
        reject(new Error("Request timeout"));
      });

      // Send body if present
      if (options.body) {
        if (typeof options.body === "string") {
          req.write(options.body);
        } else if (Buffer.isBuffer(options.body)) {
          req.write(options.body);
        } else if (options.body instanceof Readable) {
          options.body.pipe(req);
        }
      }

      req.end();
    });

    // Performance logging
    if (shouldLog) {
      const responseTime = Date.now();
      const totalDuration = responseTime - startTime;
      console.debug(`[HTTP2-FETCH] Response received: ${responseTime}ms`);
      console.debug(`[HTTP2-FETCH] Total duration: ${totalDuration}ms`);
      console.debug(`[HTTP2-FETCH] Status: ${response.status}`);
    }

    return response;
  } catch (error) {
    if (shouldLog) {
      const errorTime = Date.now();
      console.debug(
        `[HTTP2-FETCH] Request failed after ${errorTime - startTime}ms: ${error.message}`,
      );
    }

    // Handle AbortError specifically to maintain compatibility
    if (
      error.name === "AbortError" ||
      error.code === "ABORT_ERR" ||
      options.signal?.aborted
    ) {
      return new Http2Response(499, "Client Closed Request", {}, null, url);
    }

    // Fallback to HTTP/1.1 for HTTP/2 related errors
    if (
      error.code === "ERR_HTTP2_ERROR" ||
      error.code === "ERR_HTTP2_PROTOCOL_ERROR" ||
      error.code === "ERR_HTTP2_SESSION_ERROR"
    ) {
      if (shouldLog) {
        console.debug(
          `[HTTP2-FETCH] HTTP/2 error, attempting HTTP/1.1 fallback: ${error.message}`,
        );
      }
      try {
        return await fallbackToHttp1(url, options);
      } catch (fallbackError) {
        if (shouldLog) {
          console.debug(
            `[HTTP2-FETCH] HTTP/1.1 fallback also failed: ${fallbackError.message}`,
          );
        }
        throw fallbackError;
      }
    }

    throw error;
  }
}

/**
 * Get HTTP status text for status code
 */
function getStatusText(statusCode) {
  const statusTexts = {
    200: "OK",
    201: "Created",
    204: "No Content",
    400: "Bad Request",
    401: "Unauthorized",
    403: "Forbidden",
    404: "Not Found",
    429: "Too Many Requests",
    500: "Internal Server Error",
    502: "Bad Gateway",
    503: "Service Unavailable",
    499: "Client Closed Request",
  };
  return statusTexts[statusCode] || "Unknown";
}

// Export compatibility components for backward compatibility
export { Http2Response as Response };

// Create mock Agent and ProxyAgent classes for compatibility
export class Agent {
  constructor(options = {}) {
    this.options = options;
  }
}

export class ProxyAgent extends Agent {
  constructor(options = {}) {
    super(options);
    this.proxy = options.uri || options.proxy;
  }
}

// Export the main fetch function as both default and named export
export { fetch as http2Fetch };
