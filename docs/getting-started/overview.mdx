---
title: "Overview"
description: "To quickly understand what you can do with <PERSON><PERSON><PERSON>, check out each tab below."
---

<Tabs>
<Tab title="Agent">
[Agent](/features/agent/quick-start) equips the Chat model with the tools needed to handle a wide range of coding tasks

![agent](/images/agent-9ef792cfc196a3b5faa984fb072c4400.gif)

<Info>
  Learn more about [Agent](/features/agent/quick-start)
</Info>
</Tab>

<Tab title="Chat">
[Chat](/features/chat/quick-start) makes it easy to ask for help from an LLM without needing to leave the IDE

![chat](/images/chat-489b68d156be2aafe09ee7cedf233fba.gif)

<Info>
  Learn more about [Chat](/features/chat/quick-start)
</Info>
</Tab>

<Tab title="Plan">
[Plan](/features/agent/plan-mode) provides a safe environment with read-only tools for exploring code and planning changes

![plan](/images/plan-mode.gif)

<Info>
  Learn more about [Plan](/features/agent/plan-mode)
</Info>
</Tab>

<Tab title="Edit">
[Edit](/features/edit/quick-start) is a convenient way to modify code without leaving your current file

![edit](/images/edit.gif)

<Info>
  Learn more about [Edit](/features/edit/quick-start)
</Info>
</Tab>

<Tab title="Autocomplete">
[Autocomplete](/features/autocomplete/quick-start) provides inline code suggestions as you type

![autocomplete](/images/autocomplete-9d4e3f7658d3e65b8e8b20f2de939675.gif)

<Info>
  Learn more about [Autocomplete](/features/autocomplete/quick-start)
</Info>
</Tab>
</Tabs>
