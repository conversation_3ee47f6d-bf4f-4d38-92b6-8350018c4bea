---
title: "MCP Blocks"
description: "Model Context Protocol servers provide specialized functionality:"
---

- **Enable integration** with external tools and systems
- **Create extensible interfaces** for custom capabilities
- **Support complex interactions** with your development environment
- **Allow partners** to contribute specialized functionality
- **Connect to databases** to understand schema and data models during development

![MCP Blocks Overview](/images/customization/images/mcp-blocks-overview-c9a104f9b586779c156f9cf34da197c2.png)

## Learn More

Learn more in the [MCP deep dive](/customization/mcp-tools), and view [`mcpServers`](/reference#mcpservers) in the YAML Reference for more details.
