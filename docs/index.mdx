---
title: "Introduction"
icon: book-open
---

<Frame>
  <img src="/images/intro-0c302b9c15b890c251b1ad04586c880f.png" />
</Frame>

**Continue enables developers to create, share, and use custom AI code assistants with our open-source [VS Code](https://marketplace.visualstudio.com/items?itemName=Continue.continue) and [JetBrains](https://plugins.jetbrains.com/plugin/22707-continue-extension) extensions and [hub of models, rules, prompts, docs, and other building blocks](https://hub.continue.dev)**

<CardGroup cols={2}>
  <Card
    title="Chat"
    icon="messages"
    href="/features/chat/quick-start"
    horizontal
  >
    to understand and iterate on code in the sidebar
  </Card>
  <Card
    title="Autocomplete"
    icon="sparkles"
    href="/features/autocomplete/quick-start"
    horizontal
  >
    to receive inline code suggestions as you type
  </Card>
  <Card
    title="Edit"
    icon="pen-to-square"
    href="/features/edit/quick-start"
    horizontal
  >
    to modify code without leaving your current file
  </Card>
  <Card
    title="Agent"
    icon="robot"
    href="/features/agent/quick-start"
    horizontal
  >
    to make more substantial changes to your codebase
  </Card>
</CardGroup>
