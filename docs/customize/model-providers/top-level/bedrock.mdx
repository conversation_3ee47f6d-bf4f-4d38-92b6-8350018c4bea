---
title: Amazon Bedrock
slug: ../bedrock
---

Amazon Bedrock is a fully managed service on AWS that provides access to foundation models from various AI companies through a single API.

## Chat model

We recommend configuring **Claude 3.7 Sonnet** as your chat model.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Claude 3.7 Sonnet
      provider: bedrock
      model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
      env:
        region: us-east-1
        profile: bedrock
      roles:
        - chat
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "Claude 3.7 Sonnet",
        "provider": "bedrock",
        "model": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        "region": "us-east-1",
        "profile": "bedrock"
      }
    ]
  }
  ```
  </Tab>
</Tabs>

> If you run into the following error when connecting to the new Claude 3.5 Sonnet 2 models from AWS - `400 Invocation of model ID anthropic.claude-3-5-sonnet-20241022-v2:0 with on-demand throughput isn't supported. Retry your request with the ID or ARN of an inference profile that contains this model.`

> You can fix this using the following config:

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Claude 3.5 Sonnet
      provider: bedrock
      model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
      env:
        region: us-east-1
        profile: bedrock
      roles:
        - chat
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "Claude 3.5 Sonnet",
        "provider": "bedrock",
        "model": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        "region": "us-east-1",
        "profile": "bedrock"
      }
    ]
  }
  ```
  </Tab>
</Tabs>

## Autocomplete model

Bedrock currently does not offer any autocomplete models. However, [Codestral from Mistral](https://mistral.ai/news/codestral-2501/) and [Point from Poolside](https://aws.amazon.com/bedrock/poolside/) will be supported in the near future.

In the meantime, you can view a list of autocomplete model providers [here](../../model-roles/autocomplete.md).

## Embeddings model

We recommend configuring [`amazon.titan-embed-text-v2:0`](https://docs.aws.amazon.com/bedrock/latest/devguide/models.html#amazon.titan-embed-text-v2-0) as your embeddings model.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Embeddings Model
      provider: bedrock
      model: amazon.titan-embed-text-v2:0
      env:
        region: us-west-2
      roles:
        - embed
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "embeddingsProvider": {
      "title": "Embeddings Model",
      "provider": "bedrock",
      "model": "amazon.titan-embed-text-v2:0",
      "region": "us-west-2"
    }
  }
  ```
  </Tab>
</Tabs>

## Reranking model

We recommend configuring `cohere.rerank-v3-5:0` as your reranking model, you may also use `amazon.rerank-v1:0`.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Bedrock Reranker
      provider: bedrock
      model: cohere.rerank-v3-5:0
      env:
        region: us-west-2
      roles:
        - rerank
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "reranker": {
      "name": "bedrock",
      "params": {
        "model": "cohere.rerank-v3-5:0",
        "region": "us-west-2"
      }
    }
  }
  ```
  </Tab>
</Tabs>

## Prompt caching

Bedrock allows Claude models to cache tool payloads, system messages, and chat
messages between requests. Enable this behavior by adding
`promptCaching: true` under `defaultCompletionOptions` in your model
configuration.

Prompt caching is generally available for:

- Claude 3.7 Sonnet
- Claude 3.5 Haiku
- Amazon Nova Micro
- Amazon Nova Lite
- Amazon Nova Pro

Customers who were granted access to Claude 3.5 Sonnet v2 during the prompt
caching preview will retain that access, but it cannot be enabled for new users
on that model.

```yaml title="config.yaml"
models:
  - name: Claude 3.7 Sonnet
    provider: bedrock
    model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
    defaultCompletionOptions:
      promptCaching: true
```

Prompt caching is not supported in JSON configuration files, so use the YAML syntax above to enable it.

## Authentication

Authentication will be through temporary or long-term credentials in
`~/.aws/credentials` under a configured profile (e.g. "bedrock").

```title="~/.aws/credentials
[bedrock]
aws_access_key_id = abcdefg
aws_secret_access_key = hijklmno
aws_session_token = pqrstuvwxyz # Optional: means short term creds.
```

You can also use an AWS `accessKeyId` and `secretAccessKey` for authentication instead of a local credentials profile.

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Claude 3.7 Sonnet
      provider: bedrock
      model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
      env:
        region: us-east-1
        accessKeyId: ${{ secrets.AWS_ACCESS_KEY_ID }} # can also enter key inline here for local assistants
        secretAccessKey: ${{ secrets.AWS_SECRET_ACCESS_KEY }} # can also enter key inline here for local assistants
      roles:
        - chat
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "Claude 3.7 Sonnet",
        "provider": "bedrock",
        "model": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        "region": "us-east-1",
        "accessKeyId": "[YOUR_ACCESS_KEY_ID]",
        "secretAccessKey": "[YOUR_SECRET_ACCESS_KEY]" 
      }
    ]
  }
  ```
  </Tab>
</Tabs>

## Custom Imported Models

To setup Bedrock using custom imported models, add the following to your config file:

<Tabs>
  <Tab title="YAML">
  ```yaml title="config.yaml"
  models:
    - name: AWS Bedrock deepseek-coder-6.7b-instruct
      provider: bedrockimport
      model: deepseek-coder-6.7b-instruct
      env:
        region: us-west-2
        profile: bedrock
        modelArn: arn:aws:bedrock:us-west-2:XXXXX:imported-model/XXXXXX
  ```
  </Tab>
  <Tab title="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "AWS Bedrock deepseek-coder-6.7b-instruct",
        "provider": "bedrockimport",
        "model": "deepseek-coder-6.7b-instruct",
        "modelArn": "arn:aws:bedrock:us-west-2:XXXXX:imported-model/XXXXXX", 
        "region": "us-west-2",
        "profile": "bedrock"
      }
    ]
  }
  ```
  </Tab>
</Tabs>

Authentication will be through temporary or long-term credentials in
~/.aws/credentials under a configured profile (e.g. "bedrock").

```title="~/.aws/credentials
[bedrock]
aws_access_key_id = abcdefg
aws_secret_access_key = hijklmno
aws_session_token = pqrstuvwxyz # Optional: means short term creds.
```
