name: unit-testing-rules
version: 0.0.1
schema: v1
rules:
  - name: unit-testing-rules
    rule: >-
      For unit testing in this project:

      1. The project uses Vitest and Jest for testing. Prefer Vitest

      2. Run tests from within the specific package directory (e.g., `cd core && ..`

      3. For vitest tests,
        - Test files follow the pattern `*.vitest.ts`
         - Run tests using `vitest` from within the specific package/module
      directory: `cd [directory] && vitest -- [test file path]`

      4. For jest tests, 
         - Test files follow the pattern `*.test.ts`
         - Run tests using `npm test` from within the specific package/module
      directory: `cd [directory] && npm test -- [test file path]`
         - The test script uses experimental VM modules via NODE_OPTIONS flag

      5. For both
         - Write tests as top-level `test()` functions - DO NOT use `describe()`
      blocks
         - Include the function name being tested in the test description for
      clarity
