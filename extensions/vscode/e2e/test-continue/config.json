{"ui": {"displayRawMarkdown": false}, "models": [{"title": "TEST LLM", "provider": "test", "model": "this field is not used"}, {"title": "<PERSON><PERSON>", "provider": "mock", "model": "this field is not used"}, {"provider": "mock", "title": "TOOL MOCK LLM", "model": "claude-3-5-sonnet-latest", "capabilities": {"tools": true}, "requestOptions": {"extraBodyProperties": {"chatStream": [[{"role": "assistant", "content": "I'm going to call a tool:"}, {"role": "assistant", "content": "", "toolCalls": [{"id": "test_id", "type": "function", "function": {"name": "view_diff"}}]}], ["REPEAT_LAST_MSG"]]}}}, {"provider": "mock", "title": "SYSTEM MESSAGE MOCK LLM", "model": "claude-3-5-sonnet-latest", "requestOptions": {"extraBodyProperties": {"chatStream": [["REPEAT_SYSTEM_MSG"]]}}}, {"provider": "mock", "title": "LAST MESSAGE MOCK LLM", "model": "claude-3-5-sonnet-latest", "requestOptions": {"extraBodyProperties": {"chatStream": [["REPEAT_LAST_MSG"]]}}}], "systemMessage": "TEST_SYS_MSG", "analytics": {"provider": "continue-proxy"}, "tabAutocompleteModel": {"title": "TEST LLM", "provider": "test", "model": "this field is not used"}, "tabAutocompleteOptions": {"useCache": false}, "contextProviders": [{"name": "docs"}, {"name": "diff"}, {"name": "url"}, {"name": "folder"}, {"name": "terminal"}], "docs": []}